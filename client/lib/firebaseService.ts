import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where,
  DocumentData,
  QueryDocumentSnapshot,
  serverTimestamp
} from 'firebase/firestore';
import { db } from './firebase';

export interface Page {
  id?: string;
  title: string;
  slug: string;
  content: PageContent;
  seo: SEOData;
  isPublished: boolean;
  pageType: 'static' | 'dynamic';
  template?: string;
  createdAt?: any;
  updatedAt?: any;
}

export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonicalUrl?: string;
  noIndex?: boolean;
  noFollow?: boolean;
}

export interface PageContent {
  elements: ContentElement[];
}

export interface ContentElement {
  id: string;
  type: ElementType;
  order: number;
  settings: ElementSettings;
  content: any;
  styles?: ElementStyles;
  visibility?: VisibilitySettings;
}

export type ElementType =
  | 'hero'
  | 'slider'
  | 'text'
  | 'image'
  | 'gallery'
  | 'card_grid'
  | 'testimonials'
  | 'cta'
  | 'contact_form'
  | 'product_grid'
  | 'spacer'
  | 'divider'
  | 'video'
  | 'map'
  | 'custom_html';

export interface ElementSettings {
  containerWidth?: 'full' | 'container' | 'narrow';
  padding?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  margin?: {
    top: number;
    bottom: number;
  };
  background?: {
    type: 'none' | 'color' | 'image' | 'gradient';
    value: string;
    opacity?: number;
  };
  animation?: {
    type: 'none' | 'fade' | 'slide' | 'zoom';
    duration: number;
    delay: number;
  };
}

export interface ElementStyles {
  textAlign?: 'left' | 'center' | 'right';
  fontSize?: string;
  fontWeight?: string;
  color?: string;
  backgroundColor?: string;
  borderRadius?: string;
  border?: string;
  shadow?: string;
}

export interface VisibilitySettings {
  desktop: boolean;
  tablet: boolean;
  mobile: boolean;
  startDate?: Date;
  endDate?: Date;
}

// Specific content interfaces for different element types
export interface SliderContent {
  slides: SliderSlide[];
  autoplay: boolean;
  autoplaySpeed: number;
  showDots: boolean;
  showArrows: boolean;
  height: {
    mobile: number;
    desktop: number;
  };
}

export interface SliderSlide {
  id: string;
  image: string;
  title: string;
  description: string;
  cta?: {
    text: string;
    link: string;
    style: 'primary' | 'secondary' | 'outline';
  };
  overlay?: {
    enabled: boolean;
    color: string;
    opacity: number;
  };
}

export interface TextContent {
  html: string;
  plainText: string;
}

export interface ImageContent {
  src: string;
  alt: string;
  caption?: string;
  link?: string;
  size: 'small' | 'medium' | 'large' | 'full';
  alignment: 'left' | 'center' | 'right';
}

export interface GalleryContent {
  images: GalleryImage[];
  layout: 'grid' | 'masonry' | 'carousel';
  columns: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  lightbox: boolean;
  showCaptions: boolean;
}

export interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  caption?: string;
  category?: string;
}

export interface CardGridContent {
  cards: Card[];
  columns: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  cardStyle: 'default' | 'bordered' | 'shadow' | 'minimal';
}

export interface Card {
  id: string;
  title: string;
  description: string;
  image?: string;
  link?: string;
  cta?: {
    text: string;
    style: 'primary' | 'secondary' | 'outline';
  };
  badge?: string;
}

// Legacy interfaces for backward compatibility
export interface Section {
  id: string;
  type: 'hero' | 'features' | 'testimonials' | 'cta' | 'text';
  title?: string;
  content?: string;
  data?: any;
}

// Mock data for when Firebase is not available
const mockPages: Page[] = [
  {
    id: '1',
    title: 'Örnek Sayfa 1',
    slug: 'ornek-sayfa-1',
    isPublished: true,
    content: {
      hero: {
        title: 'Örnek Sayfa Başlığı',
        subtitle: 'Bu bir örnek sayfa açıklamasıdır',
        ctaText: 'Başlayın',
        ctaLink: '#'
      }
    },
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: '2',
    title: 'Taslak Sayfa',
    slug: 'taslak-sayfa',
    isPublished: false,
    content: {
      hero: {
        title: 'Taslak Sayfa',
        subtitle: 'Bu henüz yayınlanmamış bir sayfa',
        ctaText: 'Devam',
        ctaLink: '#'
      }
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Check if Firebase is available with timeout
const isFirebaseAvailable = async (): Promise<boolean> => {
  try {
    // Check if Firebase is initialized
    if (!db) {
      console.warn('Firebase not initialized, using mock data');
      return false;
    }

    // Set a 1 second timeout for Firebase check
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('Firebase timeout')), 1000)
    );

    const firebasePromise = getDocs(query(collection(db, 'pages')));

    await Promise.race([firebasePromise, timeoutPromise]);
    return true;
  } catch (error) {
    console.warn('Firebase not available, using mock data');
    return false;
  }
};

// Pages CRUD operations
export const pageService = {
  // Get all pages
  async getPages(): Promise<Page[]> {
    try {
      const firebaseAvailable = await isFirebaseAvailable();
      if (!firebaseAvailable) {
        return [...mockPages];
      }

      const q = query(collection(db, 'pages'), orderBy('updatedAt', 'desc'));
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map((doc: QueryDocumentSnapshot<DocumentData>) => ({
        id: doc.id,
        ...doc.data()
      } as Page));
    } catch (error) {
      console.error('Error fetching pages:', error);
      return [...mockPages];
    }
  },

  // Get single page by ID
  async getPage(id: string): Promise<Page | null> {
    try {
      const firebaseAvailable = await isFirebaseAvailable();
      if (!firebaseAvailable) {
        return mockPages.find(p => p.id === id) || null;
      }

      const docRef = doc(db, 'pages', id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Page;
      }
      return null;
    } catch (error) {
      console.error('Error fetching page:', error);
      return mockPages.find(p => p.id === id) || null;
    }
  },

  // Alias for getPage (for backward compatibility)
  async getPageById(id: string): Promise<Page | null> {
    return this.getPage(id);
  },

  // Get page by slug
  async getPageBySlug(slug: string): Promise<Page | null> {
    try {
      const q = query(collection(db, 'pages'), where('slug', '==', slug));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        return { id: doc.id, ...doc.data() } as Page;
      }
      return null;
    } catch (error) {
      console.error('Error fetching page by slug:', error);
      return null;
    }
  },

  // Create new page
  async createPage(page: Omit<Page, 'id' | 'createdAt' | 'updatedAt'>): Promise<string | null> {
    try {
      const firebaseAvailable = await isFirebaseAvailable();
      if (!firebaseAvailable) {
        // Mock creation - just return a fake ID
        const newId = 'mock_' + Date.now();
        mockPages.unshift({
          ...page,
          id: newId,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        return newId;
      }

      const docRef = await addDoc(collection(db, 'pages'), {
        ...page,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating page:', error);
      // Fallback to mock creation
      const newId = 'mock_' + Date.now();
      mockPages.unshift({
        ...page,
        id: newId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return newId;
    }
  },

  // Update page
  async updatePage(id: string, updates: Partial<Page>): Promise<boolean> {
    try {
      const firebaseAvailable = await isFirebaseAvailable();
      if (!firebaseAvailable) {
        // Mock update
        const pageIndex = mockPages.findIndex(p => p.id === id);
        if (pageIndex >= 0) {
          mockPages[pageIndex] = { ...mockPages[pageIndex], ...updates, updatedAt: new Date() };
          return true;
        }
        return false;
      }

      const docRef = doc(db, 'pages', id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });
      return true;
    } catch (error) {
      console.error('Error updating page:', error);
      // Fallback to mock update
      const pageIndex = mockPages.findIndex(p => p.id === id);
      if (pageIndex >= 0) {
        mockPages[pageIndex] = { ...mockPages[pageIndex], ...updates, updatedAt: new Date() };
        return true;
      }
      return false;
    }
  },

  // Delete page
  async deletePage(id: string): Promise<boolean> {
    try {
      const firebaseAvailable = await isFirebaseAvailable();
      if (!firebaseAvailable) {
        // Mock delete
        const pageIndex = mockPages.findIndex(p => p.id === id);
        if (pageIndex >= 0) {
          mockPages.splice(pageIndex, 1);
          return true;
        }
        return false;
      }

      await deleteDoc(doc(db, 'pages', id));
      return true;
    } catch (error) {
      console.error('Error deleting page:', error);
      // Fallback to mock delete
      const pageIndex = mockPages.findIndex(p => p.id === id);
      if (pageIndex >= 0) {
        mockPages.splice(pageIndex, 1);
        return true;
      }
      return false;
    }
  },

  // Get published pages only
  async getPublishedPages(): Promise<Page[]> {
    try {
      const q = query(
        collection(db, 'pages'),
        where('isPublished', '==', true),
        orderBy('updatedAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map((doc: QueryDocumentSnapshot<DocumentData>) => ({
        id: doc.id,
        ...doc.data()
      } as Page));
    } catch (error) {
      console.error('Error fetching published pages:', error);
      return [];
    }
  }
};

// Authentication helper
export const authService = {
  isAdmin: (email: string | null): boolean => {
    const adminEmail = import.meta.env.VITE_ADMIN_EMAIL;
    return email === adminEmail;
  }
};
