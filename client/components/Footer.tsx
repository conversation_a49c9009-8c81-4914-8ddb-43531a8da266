import { Zap, Github, Twitter, Linkedin, Mail } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function Footer() {
  const footerSections = [
    {
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      links: [
        { name: '<PERSON><PERSON><PERSON>', href: '/urunlerimiz' },
        { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/urunlerimiz' },
        { name: '<PERSON><PERSON>ü<PERSON>', href: '/urunlerimiz' },
        { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/urunlerimiz' },
      ],
    },
    {
      title: 'Hiz<PERSON><PERSON>',
      links: [
        { name: '<PERSON><PERSON><PERSON><PERSON><PERSON> Teslimat', href: '/hizmetlerimiz' },
        { name: '<PERSON><PERSON> Sipariş', href: '/hizmetlerimiz' },
        { name: '<PERSON><PERSON>', href: '/hizmetlerimiz' },
        { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/iletisim' },
      ],
    },
    {
      title: '<PERSON><PERSON><PERSON>',
      links: [
        { name: '<PERSON><PERSON><PERSON><PERSON>', href: '#takip' },
        { name: '<PERSON><PERSON> & <PERSON>', href: '#iade' },
        { name: 'SS<PERSON>', href: '#sss' },
        { name: 'Canlı Destek', href: '#destek' },
      ],
    },
    {
      title: 'Kurumsal',
      links: [
        { name: 'Hakkımızda', href: '/hakkimizda' },
        { name: 'Kalite', href: '/hakkimizda' },
        { name: 'Sertifikalar', href: '/hakkimizda' },
        { name: 'İletişim', href: '/iletisim' },
      ],
    },
  ];

  const socialLinks = [
    { name: 'GitHub', href: '#', icon: Github },
    { name: 'Twitter', href: '#', icon: Twitter },
    { name: 'LinkedIn', href: '#', icon: Linkedin },
    { name: 'Email', href: '#', icon: Mail },
  ];

  return (
    <footer className="bg-card border-t border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand section */}
          <div className="lg:col-span-2">
            <Link to="/" className="flex items-center space-x-2 mb-4">
              <img
                src="https://cdn.builder.io/api/v1/image/assets%2F62a3cb09456849d285bc20140bdbd847%2F2fa7f144101148d283f90821cdb02cb9?format=webp&width=200"
                alt="HORECA Logo"
                className="h-10 w-auto object-contain"
              />
            </Link>
            <p className="text-muted-foreground mb-4 max-w-sm">
              Kaliteli gıda ve içecek ürünlerinde güvenilir toptancınız.
              HORECA sektörüne özel çözümler ve hızlı teslimat.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((item) => {
                const Icon = item.icon;
                return (
                  <a
                    key={item.name}
                    href={item.href}
                    className="text-muted-foreground hover:text-foreground transition-colors duration-200"
                  >
                    <Icon className="w-5 h-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Footer sections */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className="text-sm font-semibold text-foreground mb-4">
                {section.title}
              </h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      to={link.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom section */}
        <div className="mt-12 pt-8 border-t border-border flex flex-col sm:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © 2024 HORECA. Tüm hakları saklıdır.
          </p>
          <p className="text-sm text-muted-foreground mt-2 sm:mt-0">
            Kaliteli hizmet, güvenilir ürünler 🍽️
          </p>
        </div>
      </div>
    </footer>
  );
}
