import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Menu, X, Globe, Database, Zap, FileText, Download } from 'lucide-react';
import { Link } from 'react-router-dom';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigation = [
    { name: 'Hakk<PERSON><PERSON><PERSON><PERSON>', href: '/hakkimizda' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/urunlerimiz' },
    { name: '<PERSON>z<PERSON><PERSON><PERSON><PERSON>', href: '/hizmetlerimiz' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/iletisim' },
  ];

  const catalogues = [
    {
      name: 'Newer Foods Katalog',
      description: '<PERSON><PERSON><PERSON> ürünleri kataloğu',
      file: '/Newer Foods_KatalogTR-2.pdf',
      icon: FileText
    },
    {
      name: 'TABASCO HORECA Broşür',
      description: 'TABASCO ürünleri broşürü',
      file: '/TABASCO-HORECA-BROSUR 14.04.pdf',
      icon: FileText
    },
    {
      name: 'Transmed HORECA Katalog',
      description: 'HORECA ürünleri kataloğu 2024',
      file: '/Transmed_Horeca_Catalogue_2024-2.pdf',
      icon: FileText
    }
  ];

  return (
    <header className="fixed w-full top-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border">
      <nav className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3">
              <img
                src="https://cdn.builder.io/api/v1/image/assets%2F62a3cb09456849d285bc20140bdbd847%2F2fa7f144101148d283f90821cdb02cb9?format=webp&width=200"
                alt="HORECA Logo"
                className="h-12 w-auto object-contain"
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className="text-muted-foreground hover:text-foreground transition-colors duration-200"
              >
                {item.name}
              </Link>
            ))}

            {/* Katalog Dropdown Menu */}
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="text-muted-foreground hover:text-foreground transition-colors duration-200 bg-transparent hover:bg-transparent data-[state=open]:bg-transparent">
                    Kataloglar
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid gap-3 p-6 w-[400px]">
                      <div className="row-span-3">
                        <div className="mb-2 text-lg font-medium">
                          Kataloglarımız
                        </div>
                        <p className="text-sm leading-tight text-muted-foreground mb-4">
                          Ürün kataloglarımızı indirin ve detaylı bilgi alın.
                        </p>
                      </div>
                      {catalogues.map((catalogue) => (
                        <a
                          key={catalogue.name}
                          href={catalogue.file}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                        >
                          <div className="flex items-center gap-2">
                            <catalogue.icon className="w-4 h-4" />
                            <div className="text-sm font-medium leading-none">
                              {catalogue.name}
                            </div>
                            <Download className="w-3 h-3 ml-auto" />
                          </div>
                          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                            {catalogue.description}
                          </p>
                        </a>
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>



          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-card border-t border-border">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className="block px-3 py-2 text-muted-foreground hover:text-foreground transition-colors duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Mobile Katalog Section */}
              <div className="pt-4 space-y-2">
                <div className="px-3 py-2 text-sm font-medium text-foreground">
                  Kataloglar
                </div>
                {catalogues.map((catalogue) => (
                  <a
                    key={catalogue.name}
                    href={catalogue.file}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 px-3 py-2 text-muted-foreground hover:text-foreground transition-colors duration-200"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <catalogue.icon className="w-4 h-4" />
                    <span className="flex-1">{catalogue.name}</span>
                    <Download className="w-4 h-4" />
                  </a>
                ))}
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
