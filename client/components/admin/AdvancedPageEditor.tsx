import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Save, 
  Eye, 
  Settings, 
  Plus, 
  Trash2, 
  GripVertical,
  Image as ImageIcon,
  Type,
  Layout,
  Palette,
  Monitor,
  Tablet,
  Smartphone,
  Search,
  Globe
} from 'lucide-react';
import { Page, SEOData, ContentElement, ElementType, pageService } from '@/lib/firebaseService';
import AdminAuth from './AdminAuth';
import ElementEditor from './ElementEditor';
import SEOEditor from './SEOEditor';

export default function AdvancedPageEditor() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNew = id === 'new';
  
  const [loading, setLoading] = useState(!isNew);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('content');
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  
  const [formData, setFormData] = useState<Omit<Page, 'id' | 'createdAt' | 'updatedAt'>>({
    title: '',
    slug: '',
    isPublished: false,
    pageType: 'dynamic',
    template: 'default',
    seo: {
      title: '',
      description: '',
      keywords: [],
      noIndex: false,
      noFollow: false
    },
    content: {
      elements: []
    }
  });

  useEffect(() => {
    if (!isNew && id) {
      loadPage(id);
    }
  }, [id, isNew]);

  const loadPage = async (pageId: string) => {
    setLoading(true);
    try {
      const page = await pageService.getPageById(pageId);
      if (page) {
        setFormData({
          title: page.title,
          slug: page.slug,
          isPublished: page.isPublished,
          pageType: page.pageType || 'dynamic',
          template: page.template || 'default',
          seo: page.seo || {
            title: page.title,
            description: '',
            keywords: [],
            noIndex: false,
            noFollow: false
          },
          content: page.content || { elements: [] }
        });
      }
    } catch (error) {
      setError('Sayfa yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      if (!formData.title.trim()) {
        setError('Sayfa başlığı gereklidir');
        return;
      }

      if (!formData.slug.trim()) {
        setError('Sayfa URL\'i gereklidir');
        return;
      }

      // Auto-generate SEO title if empty
      if (!formData.seo.title) {
        formData.seo.title = formData.title;
      }

      let result;
      if (isNew) {
        result = await pageService.createPage(formData);
        if (result) {
          setSuccess('Sayfa başarıyla oluşturuldu');
          setTimeout(() => {
            navigate(`/admin/pages/${result}/edit`);
          }, 1000);
        } else {
          setError('Sayfa oluşturulurken bir hata oluştu');
        }
      } else if (id) {
        result = await pageService.updatePage(id, formData);
        if (result) {
          setSuccess('Sayfa başarıyla güncellendi');
        } else {
          setError('Sayfa güncellenirken bir hata oluştu');
        }
      }
    } catch (error: any) {
      setError(error.message || 'Bir hata oluştu');
    } finally {
      setSaving(false);
    }
  };

  const generateSlug = (title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
    
    setFormData(prev => ({ ...prev, slug }));
  };

  const addElement = (type: ElementType) => {
    const newElement: ContentElement = {
      id: `element_${Date.now()}`,
      type,
      order: formData.content.elements.length,
      settings: {
        containerWidth: 'container',
        padding: { top: 16, bottom: 16, left: 0, right: 0 },
        margin: { top: 0, bottom: 0 }
      },
      content: getDefaultContentForType(type),
      visibility: {
        desktop: true,
        tablet: true,
        mobile: true
      }
    };

    setFormData(prev => ({
      ...prev,
      content: {
        ...prev.content,
        elements: [...prev.content.elements, newElement]
      }
    }));
  };

  const getDefaultContentForType = (type: ElementType) => {
    switch (type) {
      case 'slider':
        return {
          slides: [],
          autoplay: true,
          autoplaySpeed: 5000,
          showDots: true,
          showArrows: true,
          height: { mobile: 400, desktop: 600 }
        };
      case 'text':
        return {
          html: '<p>Yeni metin içeriği...</p>',
          plainText: 'Yeni metin içeriği...'
        };
      case 'image':
        return {
          src: '',
          alt: '',
          size: 'medium',
          alignment: 'center'
        };
      case 'gallery':
        return {
          images: [],
          layout: 'grid',
          columns: { mobile: 1, tablet: 2, desktop: 3 },
          lightbox: true,
          showCaptions: true
        };
      case 'card_grid':
        return {
          cards: [],
          columns: { mobile: 1, tablet: 2, desktop: 3 },
          cardStyle: 'default'
        };
      default:
        return {};
    }
  };

  const updateElement = (elementId: string, updates: Partial<ContentElement>) => {
    setFormData(prev => ({
      ...prev,
      content: {
        ...prev.content,
        elements: prev.content.elements.map(el => 
          el.id === elementId ? { ...el, ...updates } : el
        )
      }
    }));
  };

  const deleteElement = (elementId: string) => {
    setFormData(prev => ({
      ...prev,
      content: {
        ...prev.content,
        elements: prev.content.elements.filter(el => el.id !== elementId)
      }
    }));
  };

  const moveElement = (elementId: string, direction: 'up' | 'down') => {
    const elements = [...formData.content.elements];
    const index = elements.findIndex(el => el.id === elementId);
    
    if (direction === 'up' && index > 0) {
      [elements[index], elements[index - 1]] = [elements[index - 1], elements[index]];
    } else if (direction === 'down' && index < elements.length - 1) {
      [elements[index], elements[index + 1]] = [elements[index + 1], elements[index]];
    }

    // Update order values
    elements.forEach((el, idx) => {
      el.order = idx;
    });

    setFormData(prev => ({
      ...prev,
      content: { ...prev.content, elements }
    }));
  };

  if (loading) {
    return (
      <AdminAuth>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Sayfa yükleniyor...</p>
          </div>
        </div>
      </AdminAuth>
    );
  }

  return (
    <AdminAuth>
      <div className="min-h-screen bg-background">
        {/* Header */}
        <div className="border-b border-border bg-card">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">
                  {isNew ? 'Yeni Sayfa Oluştur' : 'Sayfa Düzenle'}
                </h1>
                <p className="text-muted-foreground">
                  {formData.title || 'Başlıksız Sayfa'}
                </p>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* Preview Mode Selector */}
                <div className="flex items-center space-x-2 bg-muted rounded-lg p-1">
                  <Button
                    variant={previewMode === 'desktop' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setPreviewMode('desktop')}
                  >
                    <Monitor className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={previewMode === 'tablet' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setPreviewMode('tablet')}
                  >
                    <Tablet className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={previewMode === 'mobile' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setPreviewMode('mobile')}
                  >
                    <Smartphone className="w-4 h-4" />
                  </Button>
                </div>

                <Button variant="outline" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  Önizle
                </Button>
                
                <Button onClick={handleSave} disabled={saving}>
                  <Save className="w-4 h-4 mr-2" />
                  {saving ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="content" className="flex items-center">
                <Layout className="w-4 h-4 mr-2" />
                İçerik
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center">
                <Settings className="w-4 h-4 mr-2" />
                Ayarlar
              </TabsTrigger>
              <TabsTrigger value="seo" className="flex items-center">
                <Search className="w-4 h-4 mr-2" />
                SEO
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center">
                <Eye className="w-4 h-4 mr-2" />
                Önizleme
              </TabsTrigger>
            </TabsList>

            {/* Content Tab */}
            <TabsContent value="content" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Element Palette */}
                <div className="lg:col-span-1">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Elementler</CardTitle>
                      <CardDescription>
                        Sayfanıza eklemek için elementleri sürükleyin
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {[
                        { type: 'slider' as ElementType, label: 'Slider', icon: ImageIcon },
                        { type: 'text' as ElementType, label: 'Metin', icon: Type },
                        { type: 'image' as ElementType, label: 'Resim', icon: ImageIcon },
                        { type: 'gallery' as ElementType, label: 'Galeri', icon: Layout },
                        { type: 'card_grid' as ElementType, label: 'Kart Izgara', icon: Layout }
                      ].map(({ type, label, icon: Icon }) => (
                        <Button
                          key={type}
                          variant="outline"
                          className="w-full justify-start"
                          onClick={() => addElement(type)}
                        >
                          <Icon className="w-4 h-4 mr-2" />
                          {label}
                        </Button>
                      ))}
                    </CardContent>
                  </Card>
                </div>

                {/* Content Editor */}
                <div className="lg:col-span-3">
                  <Card>
                    <CardHeader>
                      <CardTitle>Sayfa İçeriği</CardTitle>
                      <CardDescription>
                        Elementleri düzenleyin ve içeriklerini özelleştirin
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {formData.content.elements.length === 0 ? (
                        <div className="text-center py-12 text-muted-foreground">
                          <Layout className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p>Henüz element eklenmemiş</p>
                          <p className="text-sm">Sol panelden element ekleyerek başlayın</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {formData.content.elements
                            .sort((a, b) => a.order - b.order)
                            .map((element) => (
                              <ElementEditor
                                key={element.id}
                                element={element}
                                onUpdate={(updates) => updateElement(element.id, updates)}
                                onDelete={() => deleteElement(element.id)}
                                onMove={(direction) => moveElement(element.id, direction)}
                                previewMode={previewMode}
                              />
                            ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Temel Bilgiler</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="title">Sayfa Başlığı</Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => {
                          const title = e.target.value;
                          setFormData(prev => ({ ...prev, title }));
                          if (isNew && !formData.slug) {
                            generateSlug(title);
                          }
                        }}
                        placeholder="Sayfa başlığını girin"
                      />
                    </div>

                    <div>
                      <Label htmlFor="slug">URL Slug</Label>
                      <Input
                        id="slug"
                        value={formData.slug}
                        onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                        placeholder="sayfa-url"
                      />
                      <p className="text-sm text-muted-foreground mt-1">
                        Sayfa URL'i: /{formData.slug}
                      </p>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="published"
                        checked={formData.isPublished}
                        onCheckedChange={(checked) => 
                          setFormData(prev => ({ ...prev, isPublished: checked }))
                        }
                      />
                      <Label htmlFor="published">Sayfayı yayınla</Label>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Sayfa Ayarları</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="pageType">Sayfa Tipi</Label>
                      <select
                        id="pageType"
                        className="w-full p-2 border border-border rounded-md"
                        value={formData.pageType}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          pageType: e.target.value as 'static' | 'dynamic' 
                        }))}
                      >
                        <option value="dynamic">Dinamik</option>
                        <option value="static">Statik</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="template">Şablon</Label>
                      <select
                        id="template"
                        className="w-full p-2 border border-border rounded-md"
                        value={formData.template}
                        onChange={(e) => setFormData(prev => ({ ...prev, template: e.target.value }))}
                      >
                        <option value="default">Varsayılan</option>
                        <option value="landing">Landing Page</option>
                        <option value="blog">Blog</option>
                        <option value="product">Ürün</option>
                      </select>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* SEO Tab */}
            <TabsContent value="seo">
              <SEOEditor
                seoData={formData.seo}
                onChange={(seoData) => setFormData(prev => ({ ...prev, seo: seoData }))}
              />
            </TabsContent>

            {/* Preview Tab */}
            <TabsContent value="preview">
              <Card>
                <CardHeader>
                  <CardTitle>Sayfa Önizlemesi</CardTitle>
                  <CardDescription>
                    Sayfanızın nasıl görüneceğini önizleyin
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border border-border rounded-lg p-4 bg-muted/30">
                    <p className="text-center text-muted-foreground">
                      Önizleme özelliği yakında eklenecek...
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="fixed bottom-4 right-4 bg-destructive text-destructive-foreground p-4 rounded-lg shadow-lg">
            {error}
          </div>
        )}
        
        {success && (
          <div className="fixed bottom-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg">
            {success}
          </div>
        )}
      </div>
    </AdminAuth>
  );
}
