import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Globe, 
  Eye, 
  Share2, 
  Plus, 
  X,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { SEOData } from '@/lib/firebaseService';

interface SEOEditorProps {
  seoData: SEOData;
  onChange: (seoData: SEOData) => void;
}

export default function SEOEditor({ seoData, onChange }: SEOEditorProps) {
  const [newKeyword, setNewKeyword] = useState('');

  const updateSEO = (updates: Partial<SEOData>) => {
    onChange({ ...seoData, ...updates });
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !seoData.keywords.includes(newKeyword.trim())) {
      updateSEO({
        keywords: [...seoData.keywords, newKeyword.trim()]
      });
      setNewKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    updateSEO({
      keywords: seoData.keywords.filter(k => k !== keyword)
    });
  };

  const getTitleLength = () => seoData.title?.length || 0;
  const getDescriptionLength = () => seoData.description?.length || 0;

  const getTitleStatus = () => {
    const length = getTitleLength();
    if (length === 0) return { status: 'error', message: 'Başlık gereklidir' };
    if (length < 30) return { status: 'warning', message: 'Başlık çok kısa (30+ karakter önerilir)' };
    if (length > 60) return { status: 'warning', message: 'Başlık çok uzun (60 karakter önerilir)' };
    return { status: 'success', message: 'Başlık uzunluğu ideal' };
  };

  const getDescriptionStatus = () => {
    const length = getDescriptionLength();
    if (length === 0) return { status: 'error', message: 'Açıklama gereklidir' };
    if (length < 120) return { status: 'warning', message: 'Açıklama çok kısa (120+ karakter önerilir)' };
    if (length > 160) return { status: 'warning', message: 'Açıklama çok uzun (160 karakter önerilir)' };
    return { status: 'success', message: 'Açıklama uzunluğu ideal' };
  };

  const StatusIcon = ({ status }: { status: string }) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  const titleStatus = getTitleStatus();
  const descriptionStatus = getDescriptionStatus();

  return (
    <div className="space-y-6">
      {/* Basic SEO */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="w-5 h-5 mr-2" />
            Temel SEO Ayarları
          </CardTitle>
          <CardDescription>
            Arama motorları için sayfa başlığı ve açıklaması
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="seo-title">SEO Başlığı</Label>
              <div className="flex items-center space-x-2">
                <StatusIcon status={titleStatus.status} />
                <span className="text-sm text-muted-foreground">
                  {getTitleLength()}/60
                </span>
              </div>
            </div>
            <Input
              id="seo-title"
              value={seoData.title || ''}
              onChange={(e) => updateSEO({ title: e.target.value })}
              placeholder="Sayfa başlığı (arama sonuçlarında görünecek)"
              maxLength={60}
            />
            <p className="text-sm text-muted-foreground mt-1">
              {titleStatus.message}
            </p>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="seo-description">SEO Açıklaması</Label>
              <div className="flex items-center space-x-2">
                <StatusIcon status={descriptionStatus.status} />
                <span className="text-sm text-muted-foreground">
                  {getDescriptionLength()}/160
                </span>
              </div>
            </div>
            <Textarea
              id="seo-description"
              value={seoData.description || ''}
              onChange={(e) => updateSEO({ description: e.target.value })}
              placeholder="Sayfa açıklaması (arama sonuçlarında görünecek)"
              rows={3}
              maxLength={160}
            />
            <p className="text-sm text-muted-foreground mt-1">
              {descriptionStatus.message}
            </p>
          </div>

          <div>
            <Label>Anahtar Kelimeler</Label>
            <div className="flex space-x-2 mb-2">
              <Input
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                placeholder="Anahtar kelime ekle"
                onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
              />
              <Button onClick={addKeyword} size="sm">
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {seoData.keywords.map((keyword, index) => (
                <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                  <span>{keyword}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 ml-1"
                    onClick={() => removeKeyword(keyword)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </Badge>
              ))}
            </div>
            {seoData.keywords.length === 0 && (
              <p className="text-sm text-muted-foreground mt-2">
                Henüz anahtar kelime eklenmemiş
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Open Graph */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Share2 className="w-5 h-5 mr-2" />
            Sosyal Medya (Open Graph)
          </CardTitle>
          <CardDescription>
            Sosyal medyada paylaşıldığında nasıl görüneceği
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="og-title">OG Başlığı</Label>
            <Input
              id="og-title"
              value={seoData.ogTitle || ''}
              onChange={(e) => updateSEO({ ogTitle: e.target.value })}
              placeholder={seoData.title || 'SEO başlığı kullanılacak'}
            />
            <p className="text-sm text-muted-foreground mt-1">
              Boş bırakılırsa SEO başlığı kullanılır
            </p>
          </div>

          <div>
            <Label htmlFor="og-description">OG Açıklaması</Label>
            <Textarea
              id="og-description"
              value={seoData.ogDescription || ''}
              onChange={(e) => updateSEO({ ogDescription: e.target.value })}
              placeholder={seoData.description || 'SEO açıklaması kullanılacak'}
              rows={3}
            />
            <p className="text-sm text-muted-foreground mt-1">
              Boş bırakılırsa SEO açıklaması kullanılır
            </p>
          </div>

          <div>
            <Label htmlFor="og-image">OG Resmi</Label>
            <Input
              id="og-image"
              value={seoData.ogImage || ''}
              onChange={(e) => updateSEO({ ogImage: e.target.value })}
              placeholder="https://example.com/image.jpg"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Sosyal medyada paylaşıldığında gösterilecek resim (1200x630 önerilir)
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Advanced SEO */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Globe className="w-5 h-5 mr-2" />
            Gelişmiş SEO Ayarları
          </CardTitle>
          <CardDescription>
            Arama motoru davranışını kontrol eden ayarlar
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="canonical-url">Canonical URL</Label>
            <Input
              id="canonical-url"
              value={seoData.canonicalUrl || ''}
              onChange={(e) => updateSEO({ canonicalUrl: e.target.value })}
              placeholder="https://example.com/sayfa-url"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Sayfanın asıl URL'si (duplicate content önlemek için)
            </p>
          </div>

          <Separator />

          <div className="space-y-4">
            <h4 className="font-medium">Robot Direktifleri</h4>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="no-index"
                checked={seoData.noIndex || false}
                onCheckedChange={(checked) => updateSEO({ noIndex: checked })}
              />
              <Label htmlFor="no-index">No Index</Label>
              <span className="text-sm text-muted-foreground">
                (Arama motorlarında indekslenmesini engelle)
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="no-follow"
                checked={seoData.noFollow || false}
                onCheckedChange={(checked) => updateSEO({ noFollow: checked })}
              />
              <Label htmlFor="no-follow">No Follow</Label>
              <span className="text-sm text-muted-foreground">
                (Sayfa linklerini takip etmesini engelle)
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SEO Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="w-5 h-5 mr-2" />
            Arama Sonucu Önizlemesi
          </CardTitle>
          <CardDescription>
            Google'da nasıl görüneceğinin önizlemesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border border-border rounded-lg p-4 bg-muted/30">
            <div className="space-y-1">
              <div className="text-blue-600 text-lg hover:underline cursor-pointer">
                {seoData.title || 'Sayfa Başlığı'}
              </div>
              <div className="text-green-700 text-sm">
                https://example.com/{seoData.canonicalUrl ? seoData.canonicalUrl.replace(/^https?:\/\/[^\/]+/, '') : 'sayfa-url'}
              </div>
              <div className="text-gray-600 text-sm">
                {seoData.description || 'Sayfa açıklaması burada görünecek...'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Social Media Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Share2 className="w-5 h-5 mr-2" />
            Sosyal Medya Önizlemesi
          </CardTitle>
          <CardDescription>
            Facebook, Twitter vb. platformlarda nasıl görüneceği
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border border-border rounded-lg overflow-hidden bg-white">
            {seoData.ogImage && (
              <div className="aspect-video bg-muted flex items-center justify-center">
                <img 
                  src={seoData.ogImage} 
                  alt="OG Preview" 
                  className="max-w-full max-h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              </div>
            )}
            <div className="p-4">
              <div className="text-sm text-gray-500 uppercase tracking-wide">
                example.com
              </div>
              <div className="font-semibold text-gray-900 mt-1">
                {seoData.ogTitle || seoData.title || 'Sayfa Başlığı'}
              </div>
              <div className="text-gray-600 text-sm mt-1">
                {seoData.ogDescription || seoData.description || 'Sayfa açıklaması...'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
