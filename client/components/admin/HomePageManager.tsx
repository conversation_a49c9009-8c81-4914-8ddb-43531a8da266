import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Save, 
  Plus, 
  Trash2, 
  Image as ImageIcon,
  Settings,
  Eye,
  X,
  GripVertical
} from 'lucide-react';
import AdminAuth from './AdminAuth';

interface SliderSlide {
  id: string;
  image: string;
  title: string;
  description: string;
  ctaText?: string;
  ctaLink?: string;
  order: number;
}

interface ProductCategory {
  id: string;
  title: string;
  image: string;
  description: string;
  order: number;
}

interface HomePageContent {
  slider: {
    enabled: boolean;
    autoplay: boolean;
    autoplaySpeed: number;
    height: {
      mobile: number;
      desktop: number;
    };
    slides: SliderSlide[];
  };
  productCategories: {
    enabled: boolean;
    title: string;
    subtitle: string;
    description: string;
    categories: ProductCategory[];
  };
  features: {
    enabled: boolean;
    title: string;
    subtitle: string;
    items: Array<{
      id: string;
      icon: string;
      title: string;
      description: string;
    }>;
  };
}

export default function HomePageManager() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('slider');

  const [content, setContent] = useState<HomePageContent>({
    slider: {
      enabled: true,
      autoplay: true,
      autoplaySpeed: 5000,
      height: {
        mobile: 650,
        desktop: 750
      },
      slides: []
    },
    productCategories: {
      enabled: true,
      title: 'Geniş Ürün Yelpazesi',
      subtitle: 'Ürünlerimiz',
      description: 'Süt ürünlerinden deniz ürünlerine, meyve sebzeden soslar ve baharatlara kadar geniş ürün gamımızla hizmetinizdeyiz.',
      categories: []
    },
    features: {
      enabled: true,
      title: 'Neden Bizi Seçmelisiniz?',
      subtitle: 'Özelliklerimiz',
      items: []
    }
  });

  useEffect(() => {
    loadHomePageContent();
  }, []);

  const loadHomePageContent = async () => {
    setLoading(true);
    try {
      // Burada Firebase'den ana sayfa içeriği yüklenecek
      // Şimdilik mevcut statik içeriği kullanıyoruz
      const defaultSlides: SliderSlide[] = [
        {
          id: '1',
          image: '/1.1.png',
          title: 'Kaliteli Gıda Ürünleri',
          description: '2003\'ten beri güvenilir tedarikçiniz',
          ctaText: 'Keşfedin',
          ctaLink: '#',
          order: 0
        },
        {
          id: '2',
          image: '/2.jpg',
          title: '3.000 m² Kapalı Depo',
          description: 'Modern depolama ve lojistik hizmetleri',
          ctaText: 'Keşfedin',
          ctaLink: '#',
          order: 1
        },
        {
          id: '3',
          image: '/3.1.png',
          title: 'HORECA Çözümleri',
          description: 'Otel, restoran ve cafe işletmelerine özel',
          ctaText: 'Keşfedin',
          ctaLink: '#',
          order: 2
        }
      ];

      const defaultCategories: ProductCategory[] = [
        {
          id: '1',
          title: 'Süt Ürünleri',
          image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=300&h=200&fit=crop',
          description: 'Peynir, yoğurt, süt ve tüm süt ürünleri',
          order: 0
        },
        {
          id: '2',
          title: 'Et ve Şarküteri',
          image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=300&h=200&fit=crop',
          description: 'Taze et, donuk et ve şarküteri ürünleri',
          order: 1
        },
        {
          id: '3',
          title: 'Meyve & Sebze',
          image: 'https://images.unsplash.com/photo-1506976785307-8732e854ad03?w=300&h=200&fit=crop',
          description: 'Taze meyve ve sebze çeşitleri',
          order: 2
        },
        {
          id: '4',
          title: 'Deniz Ürünleri',
          image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=300&h=200&fit=crop',
          description: 'Taze ve donuk balık çeşitleri',
          order: 3
        }
      ];

      setContent(prev => ({
        ...prev,
        slider: {
          ...prev.slider,
          slides: defaultSlides
        },
        productCategories: {
          ...prev.productCategories,
          categories: defaultCategories
        }
      }));
    } catch (error) {
      setError('İçerik yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const saveContent = async () => {
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // Burada Firebase'e kaydetme işlemi yapılacak
      // Şimdilik localStorage'a kaydediyoruz
      localStorage.setItem('homePageContent', JSON.stringify(content));
      setSuccess('Ana sayfa içeriği başarıyla kaydedildi');
    } catch (error) {
      setError('Kaydetme sırasında bir hata oluştu');
    } finally {
      setSaving(false);
    }
  };

  const addSlide = () => {
    const newSlide: SliderSlide = {
      id: `slide_${Date.now()}`,
      image: '',
      title: 'Yeni Slide',
      description: 'Slide açıklaması',
      ctaText: 'Keşfedin',
      ctaLink: '#',
      order: content.slider.slides.length
    };

    setContent(prev => ({
      ...prev,
      slider: {
        ...prev.slider,
        slides: [...prev.slider.slides, newSlide]
      }
    }));
  };

  const updateSlide = (slideId: string, updates: Partial<SliderSlide>) => {
    setContent(prev => ({
      ...prev,
      slider: {
        ...prev.slider,
        slides: prev.slider.slides.map(slide =>
          slide.id === slideId ? { ...slide, ...updates } : slide
        )
      }
    }));
  };

  const deleteSlide = (slideId: string) => {
    setContent(prev => ({
      ...prev,
      slider: {
        ...prev.slider,
        slides: prev.slider.slides.filter(slide => slide.id !== slideId)
      }
    }));
  };

  const addCategory = () => {
    const newCategory: ProductCategory = {
      id: `category_${Date.now()}`,
      title: 'Yeni Kategori',
      image: '',
      description: 'Kategori açıklaması',
      order: content.productCategories.categories.length
    };

    setContent(prev => ({
      ...prev,
      productCategories: {
        ...prev.productCategories,
        categories: [...prev.productCategories.categories, newCategory]
      }
    }));
  };

  const updateCategory = (categoryId: string, updates: Partial<ProductCategory>) => {
    setContent(prev => ({
      ...prev,
      productCategories: {
        ...prev.productCategories,
        categories: prev.productCategories.categories.map(category =>
          category.id === categoryId ? { ...category, ...updates } : category
        )
      }
    }));
  };

  const deleteCategory = (categoryId: string) => {
    setContent(prev => ({
      ...prev,
      productCategories: {
        ...prev.productCategories,
        categories: prev.productCategories.categories.filter(category => category.id !== categoryId)
      }
    }));
  };

  if (loading) {
    return (
      <AdminAuth>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Ana sayfa içeriği yükleniyor...</p>
          </div>
        </div>
      </AdminAuth>
    );
  }

  return (
    <AdminAuth>
      <div className="min-h-screen bg-background">
        {/* Header */}
        <div className="border-b border-border bg-card">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">Ana Sayfa Yönetimi</h1>
                <p className="text-muted-foreground">
                  Ana sayfanızın tüm içeriklerini buradan yönetebilirsiniz
                </p>
              </div>
              
              <Button onClick={saveContent} disabled={saving}>
                <Save className="w-4 h-4 mr-2" />
                {saving ? 'Kaydediliyor...' : 'Kaydet'}
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="slider">Slider</TabsTrigger>
              <TabsTrigger value="categories">Ürün Kategorileri</TabsTrigger>
              <TabsTrigger value="features">Özellikler</TabsTrigger>
            </TabsList>

            {/* Slider Tab */}
            <TabsContent value="slider" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Ana Sayfa Slider</CardTitle>
                      <CardDescription>
                        Ana sayfanın üst kısmındaki slider'ı yönetin
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={content.slider.enabled}
                          onCheckedChange={(checked) => 
                            setContent(prev => ({
                              ...prev,
                              slider: { ...prev.slider, enabled: checked }
                            }))
                          }
                        />
                        <Label>Slider Aktif</Label>
                      </div>
                      <Button onClick={addSlide}>
                        <Plus className="w-4 h-4 mr-2" />
                        Slide Ekle
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Slider Settings */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/30 rounded-lg">
                    <div>
                      <Label>Otomatik Oynatma</Label>
                      <Switch
                        checked={content.slider.autoplay}
                        onCheckedChange={(checked) => 
                          setContent(prev => ({
                            ...prev,
                            slider: { ...prev.slider, autoplay: checked }
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label>Oynatma Hızı (ms)</Label>
                      <Input
                        type="number"
                        value={content.slider.autoplaySpeed}
                        onChange={(e) => 
                          setContent(prev => ({
                            ...prev,
                            slider: { ...prev.slider, autoplaySpeed: parseInt(e.target.value) }
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label>Mobil Yükseklik (px)</Label>
                      <Input
                        type="number"
                        value={content.slider.height.mobile}
                        onChange={(e) => 
                          setContent(prev => ({
                            ...prev,
                            slider: { 
                              ...prev.slider, 
                              height: { 
                                ...prev.slider.height, 
                                mobile: parseInt(e.target.value) 
                              } 
                            }
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label>Desktop Yükseklik (px)</Label>
                      <Input
                        type="number"
                        value={content.slider.height.desktop}
                        onChange={(e) => 
                          setContent(prev => ({
                            ...prev,
                            slider: { 
                              ...prev.slider, 
                              height: { 
                                ...prev.slider.height, 
                                desktop: parseInt(e.target.value) 
                              } 
                            }
                          }))
                        }
                      />
                    </div>
                  </div>

                  {/* Slides */}
                  <div className="space-y-4">
                    {content.slider.slides
                      .sort((a, b) => a.order - b.order)
                      .map((slide, index) => (
                        <Card key={slide.id} className="border-l-4 border-l-primary">
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <GripVertical className="w-4 h-4 text-muted-foreground cursor-move" />
                                <div>
                                  <CardTitle className="text-base">Slide {index + 1}</CardTitle>
                                  <Badge variant="secondary" className="text-xs">
                                    {slide.title}
                                  </Badge>
                                </div>
                              </div>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => deleteSlide(slide.id)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <Label>Resim URL</Label>
                                <Input
                                  value={slide.image}
                                  onChange={(e) => updateSlide(slide.id, { image: e.target.value })}
                                  placeholder="/resim.jpg"
                                />
                              </div>
                              <div>
                                <Label>Başlık</Label>
                                <Input
                                  value={slide.title}
                                  onChange={(e) => updateSlide(slide.id, { title: e.target.value })}
                                />
                              </div>
                              <div className="md:col-span-2">
                                <Label>Açıklama</Label>
                                <Textarea
                                  value={slide.description}
                                  onChange={(e) => updateSlide(slide.id, { description: e.target.value })}
                                  rows={2}
                                />
                              </div>
                              <div>
                                <Label>Buton Metni</Label>
                                <Input
                                  value={slide.ctaText || ''}
                                  onChange={(e) => updateSlide(slide.id, { ctaText: e.target.value })}
                                  placeholder="Keşfedin"
                                />
                              </div>
                              <div>
                                <Label>Buton Linki</Label>
                                <Input
                                  value={slide.ctaLink || ''}
                                  onChange={(e) => updateSlide(slide.id, { ctaLink: e.target.value })}
                                  placeholder="#"
                                />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    
                    {content.slider.slides.length === 0 && (
                      <div className="text-center py-12 text-muted-foreground">
                        <ImageIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>Henüz slide eklenmemiş</p>
                        <p className="text-sm">Yukarıdaki "Slide Ekle" butonuna tıklayarak başlayın</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Categories Tab */}
            <TabsContent value="categories" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Ürün Kategorileri</CardTitle>
                      <CardDescription>
                        Ana sayfada gösterilen ürün kategorilerini yönetin
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={content.productCategories.enabled}
                          onCheckedChange={(checked) => 
                            setContent(prev => ({
                              ...prev,
                              productCategories: { ...prev.productCategories, enabled: checked }
                            }))
                          }
                        />
                        <Label>Kategoriler Aktif</Label>
                      </div>
                      <Button onClick={addCategory}>
                        <Plus className="w-4 h-4 mr-2" />
                        Kategori Ekle
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Section Settings */}
                  <div className="grid grid-cols-1 gap-4 p-4 bg-muted/30 rounded-lg">
                    <div>
                      <Label>Bölüm Başlığı</Label>
                      <Input
                        value={content.productCategories.title}
                        onChange={(e) => 
                          setContent(prev => ({
                            ...prev,
                            productCategories: { ...prev.productCategories, title: e.target.value }
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label>Alt Başlık</Label>
                      <Input
                        value={content.productCategories.subtitle}
                        onChange={(e) => 
                          setContent(prev => ({
                            ...prev,
                            productCategories: { ...prev.productCategories, subtitle: e.target.value }
                          }))
                        }
                      />
                    </div>
                    <div>
                      <Label>Açıklama</Label>
                      <Textarea
                        value={content.productCategories.description}
                        onChange={(e) => 
                          setContent(prev => ({
                            ...prev,
                            productCategories: { ...prev.productCategories, description: e.target.value }
                          }))
                        }
                        rows={3}
                      />
                    </div>
                  </div>

                  {/* Categories */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {content.productCategories.categories
                      .sort((a, b) => a.order - b.order)
                      .map((category, index) => (
                        <Card key={category.id} className="border-l-4 border-l-blue-500">
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div>
                                <CardTitle className="text-base">{category.title}</CardTitle>
                                <Badge variant="outline" className="text-xs">
                                  Kategori {index + 1}
                                </Badge>
                              </div>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => deleteCategory(category.id)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div>
                              <Label>Kategori Adı</Label>
                              <Input
                                value={category.title}
                                onChange={(e) => updateCategory(category.id, { title: e.target.value })}
                              />
                            </div>
                            <div>
                              <Label>Resim URL</Label>
                              <Input
                                value={category.image}
                                onChange={(e) => updateCategory(category.id, { image: e.target.value })}
                                placeholder="https://example.com/image.jpg"
                              />
                            </div>
                            <div>
                              <Label>Açıklama</Label>
                              <Textarea
                                value={category.description}
                                onChange={(e) => updateCategory(category.id, { description: e.target.value })}
                                rows={2}
                              />
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>

                  {content.productCategories.categories.length === 0 && (
                    <div className="text-center py-12 text-muted-foreground">
                      <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>Henüz kategori eklenmemiş</p>
                      <p className="text-sm">Yukarıdaki "Kategori Ekle" butonuna tıklayarak başlayın</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Features Tab */}
            <TabsContent value="features" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Özellikler Bölümü</CardTitle>
                  <CardDescription>
                    Ana sayfada gösterilen özellikler bölümünü yönetin
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Özellikler yönetimi yakında eklenecek...</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="fixed bottom-4 right-4 bg-destructive text-destructive-foreground p-4 rounded-lg shadow-lg">
            {error}
          </div>
        )}
        
        {success && (
          <div className="fixed bottom-4 right-4 bg-green-600 text-white p-4 rounded-lg shadow-lg">
            {success}
          </div>
        )}
      </div>
    </AdminAuth>
  );
}
