import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ChevronUp, 
  ChevronDown, 
  Trash2, 
  Settings, 
  Eye, 
  EyeOff,
  GripVertical,
  Image as ImageIcon,
  Type,
  Layout,
  Palette,
  Monitor,
  Tablet,
  Smartphone,
  Plus,
  X
} from 'lucide-react';
import { ContentElement, SliderContent, SliderSlide, TextContent, ImageContent, GalleryContent, CardGridContent } from '@/lib/firebaseService';

interface ElementEditorProps {
  element: ContentElement;
  onUpdate: (updates: Partial<ContentElement>) => void;
  onDelete: () => void;
  onMove: (direction: 'up' | 'down') => void;
  previewMode: 'desktop' | 'tablet' | 'mobile';
}

export default function ElementEditor({ element, onUpdate, onDelete, onMove, previewMode }: ElementEditorProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('content');

  const getElementTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      slider: 'Slider',
      text: 'Metin',
      image: 'Resim',
      gallery: 'Galeri',
      card_grid: 'Kart Izgara',
      hero: 'Hero Bölümü',
      cta: 'Çağrı Butonu',
      contact_form: 'İletişim Formu',
      product_grid: 'Ürün Izgarası',
      spacer: 'Boşluk',
      divider: 'Ayırıcı',
      video: 'Video',
      map: 'Harita',
      custom_html: 'Özel HTML'
    };
    return labels[type] || type;
  };

  const updateContent = (updates: any) => {
    onUpdate({ content: { ...element.content, ...updates } });
  };

  const updateSettings = (updates: any) => {
    onUpdate({ settings: { ...element.settings, ...updates } });
  };

  const updateStyles = (updates: any) => {
    onUpdate({ styles: { ...element.styles, ...updates } });
  };

  const updateVisibility = (updates: any) => {
    onUpdate({ visibility: { ...element.visibility, ...updates } });
  };

  const renderSliderEditor = () => {
    const content = element.content as SliderContent;
    
    const addSlide = () => {
      const newSlide: SliderSlide = {
        id: `slide_${Date.now()}`,
        image: '',
        title: 'Yeni Slide',
        description: 'Slide açıklaması',
        overlay: {
          enabled: true,
          color: '#000000',
          opacity: 0.5
        }
      };
      
      updateContent({
        slides: [...(content.slides || []), newSlide]
      });
    };

    const updateSlide = (slideId: string, updates: Partial<SliderSlide>) => {
      updateContent({
        slides: content.slides?.map(slide => 
          slide.id === slideId ? { ...slide, ...updates } : slide
        ) || []
      });
    };

    const deleteSlide = (slideId: string) => {
      updateContent({
        slides: content.slides?.filter(slide => slide.id !== slideId) || []
      });
    };

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Slider Ayarları</h4>
          <Button size="sm" onClick={addSlide}>
            <Plus className="w-4 h-4 mr-2" />
            Slide Ekle
          </Button>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Otomatik Oynatma</Label>
            <Switch
              checked={content.autoplay || false}
              onCheckedChange={(checked) => updateContent({ autoplay: checked })}
            />
          </div>
          <div>
            <Label>Oynatma Hızı (ms)</Label>
            <Input
              type="number"
              value={content.autoplaySpeed || 5000}
              onChange={(e) => updateContent({ autoplaySpeed: parseInt(e.target.value) })}
            />
          </div>
          <div>
            <Label>Nokta Göstergesi</Label>
            <Switch
              checked={content.showDots !== false}
              onCheckedChange={(checked) => updateContent({ showDots: checked })}
            />
          </div>
          <div>
            <Label>Ok Butonları</Label>
            <Switch
              checked={content.showArrows !== false}
              onCheckedChange={(checked) => updateContent({ showArrows: checked })}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Mobil Yükseklik (px)</Label>
            <Input
              type="number"
              value={content.height?.mobile || 400}
              onChange={(e) => updateContent({ 
                height: { 
                  ...content.height, 
                  mobile: parseInt(e.target.value) 
                } 
              })}
            />
          </div>
          <div>
            <Label>Desktop Yükseklik (px)</Label>
            <Input
              type="number"
              value={content.height?.desktop || 600}
              onChange={(e) => updateContent({ 
                height: { 
                  ...content.height, 
                  desktop: parseInt(e.target.value) 
                } 
              })}
            />
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h4 className="font-medium">Slide'lar</h4>
          {content.slides?.map((slide, index) => (
            <Card key={slide.id} className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h5 className="font-medium">Slide {index + 1}</h5>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => deleteSlide(slide.id)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label>Resim URL</Label>
                  <Input
                    value={slide.image}
                    onChange={(e) => updateSlide(slide.id, { image: e.target.value })}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                
                <div>
                  <Label>Başlık</Label>
                  <Input
                    value={slide.title}
                    onChange={(e) => updateSlide(slide.id, { title: e.target.value })}
                  />
                </div>
                
                <div>
                  <Label>Açıklama</Label>
                  <Textarea
                    value={slide.description}
                    onChange={(e) => updateSlide(slide.id, { description: e.target.value })}
                    rows={3}
                  />
                </div>

                {slide.cta && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Buton Metni</Label>
                      <Input
                        value={slide.cta.text}
                        onChange={(e) => updateSlide(slide.id, { 
                          cta: { ...slide.cta, text: e.target.value } 
                        })}
                      />
                    </div>
                    <div>
                      <Label>Buton Linki</Label>
                      <Input
                        value={slide.cta.link}
                        onChange={(e) => updateSlide(slide.id, { 
                          cta: { ...slide.cta, link: e.target.value } 
                        })}
                      />
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={slide.overlay?.enabled || false}
                      onCheckedChange={(checked) => updateSlide(slide.id, {
                        overlay: { ...slide.overlay, enabled: checked }
                      })}
                    />
                    <Label>Overlay</Label>
                  </div>
                  
                  {slide.overlay?.enabled && (
                    <>
                      <div>
                        <Label>Renk</Label>
                        <Input
                          type="color"
                          value={slide.overlay.color || '#000000'}
                          onChange={(e) => updateSlide(slide.id, {
                            overlay: { ...slide.overlay, color: e.target.value }
                          })}
                          className="w-16 h-8"
                        />
                      </div>
                      <div>
                        <Label>Opaklık</Label>
                        <Input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={slide.overlay.opacity || 0.5}
                          onChange={(e) => updateSlide(slide.id, {
                            overlay: { ...slide.overlay, opacity: parseFloat(e.target.value) }
                          })}
                          className="w-20"
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            </Card>
          )) || (
            <p className="text-muted-foreground text-center py-4">
              Henüz slide eklenmemiş. Yukarıdaki butona tıklayarak slide ekleyin.
            </p>
          )}
        </div>
      </div>
    );
  };

  const renderTextEditor = () => {
    const content = element.content as TextContent;
    
    return (
      <div className="space-y-4">
        <div>
          <Label>Metin İçeriği</Label>
          <Textarea
            value={content.html || ''}
            onChange={(e) => updateContent({ 
              html: e.target.value,
              plainText: e.target.value.replace(/<[^>]*>/g, '')
            })}
            rows={8}
            placeholder="HTML içerik girin..."
          />
          <p className="text-sm text-muted-foreground mt-1">
            HTML etiketleri kullanabilirsiniz
          </p>
        </div>
      </div>
    );
  };

  const renderImageEditor = () => {
    const content = element.content as ImageContent;
    
    return (
      <div className="space-y-4">
        <div>
          <Label>Resim URL</Label>
          <Input
            value={content.src || ''}
            onChange={(e) => updateContent({ src: e.target.value })}
            placeholder="https://example.com/image.jpg"
          />
        </div>
        
        <div>
          <Label>Alt Metni</Label>
          <Input
            value={content.alt || ''}
            onChange={(e) => updateContent({ alt: e.target.value })}
            placeholder="Resim açıklaması"
          />
        </div>

        <div>
          <Label>Açıklama (İsteğe bağlı)</Label>
          <Input
            value={content.caption || ''}
            onChange={(e) => updateContent({ caption: e.target.value })}
            placeholder="Resim açıklaması"
          />
        </div>

        <div>
          <Label>Link (İsteğe bağlı)</Label>
          <Input
            value={content.link || ''}
            onChange={(e) => updateContent({ link: e.target.value })}
            placeholder="https://example.com"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Boyut</Label>
            <select
              value={content.size || 'medium'}
              onChange={(e) => updateContent({ size: e.target.value })}
              className="w-full p-2 border border-border rounded-md"
            >
              <option value="small">Küçük</option>
              <option value="medium">Orta</option>
              <option value="large">Büyük</option>
              <option value="full">Tam Genişlik</option>
            </select>
          </div>
          
          <div>
            <Label>Hizalama</Label>
            <select
              value={content.alignment || 'center'}
              onChange={(e) => updateContent({ alignment: e.target.value })}
              className="w-full p-2 border border-border rounded-md"
            >
              <option value="left">Sol</option>
              <option value="center">Orta</option>
              <option value="right">Sağ</option>
            </select>
          </div>
        </div>
      </div>
    );
  };

  const renderContentEditor = () => {
    switch (element.type) {
      case 'slider':
        return renderSliderEditor();
      case 'text':
        return renderTextEditor();
      case 'image':
        return renderImageEditor();
      default:
        return (
          <div className="text-center py-8 text-muted-foreground">
            <p>Bu element tipi için editör henüz hazırlanmamış</p>
            <p className="text-sm">Element tipi: {element.type}</p>
          </div>
        );
    }
  };

  const renderSettingsEditor = () => {
    return (
      <div className="space-y-4">
        <div>
          <Label>Container Genişliği</Label>
          <select
            value={element.settings?.containerWidth || 'container'}
            onChange={(e) => updateSettings({ containerWidth: e.target.value })}
            className="w-full p-2 border border-border rounded-md"
          >
            <option value="full">Tam Genişlik</option>
            <option value="container">Container</option>
            <option value="narrow">Dar</option>
          </select>
        </div>

        <div>
          <Label>Padding</Label>
          <div className="grid grid-cols-4 gap-2">
            <Input
              type="number"
              placeholder="Üst"
              value={element.settings?.padding?.top || 16}
              onChange={(e) => updateSettings({
                padding: { 
                  ...element.settings?.padding, 
                  top: parseInt(e.target.value) 
                }
              })}
            />
            <Input
              type="number"
              placeholder="Sağ"
              value={element.settings?.padding?.right || 0}
              onChange={(e) => updateSettings({
                padding: { 
                  ...element.settings?.padding, 
                  right: parseInt(e.target.value) 
                }
              })}
            />
            <Input
              type="number"
              placeholder="Alt"
              value={element.settings?.padding?.bottom || 16}
              onChange={(e) => updateSettings({
                padding: { 
                  ...element.settings?.padding, 
                  bottom: parseInt(e.target.value) 
                }
              })}
            />
            <Input
              type="number"
              placeholder="Sol"
              value={element.settings?.padding?.left || 0}
              onChange={(e) => updateSettings({
                padding: { 
                  ...element.settings?.padding, 
                  left: parseInt(e.target.value) 
                }
              })}
            />
          </div>
        </div>

        <div>
          <Label>Margin</Label>
          <div className="grid grid-cols-2 gap-2">
            <Input
              type="number"
              placeholder="Üst"
              value={element.settings?.margin?.top || 0}
              onChange={(e) => updateSettings({
                margin: { 
                  ...element.settings?.margin, 
                  top: parseInt(e.target.value) 
                }
              })}
            />
            <Input
              type="number"
              placeholder="Alt"
              value={element.settings?.margin?.bottom || 0}
              onChange={(e) => updateSettings({
                margin: { 
                  ...element.settings?.margin, 
                  bottom: parseInt(e.target.value) 
                }
              })}
            />
          </div>
        </div>
      </div>
    );
  };

  const renderVisibilityEditor = () => {
    return (
      <div className="space-y-4">
        <div>
          <Label>Görünürlük</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                checked={element.visibility?.desktop !== false}
                onCheckedChange={(checked) => updateVisibility({ desktop: checked })}
              />
              <Monitor className="w-4 h-4" />
              <Label>Desktop</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={element.visibility?.tablet !== false}
                onCheckedChange={(checked) => updateVisibility({ tablet: checked })}
              />
              <Tablet className="w-4 h-4" />
              <Label>Tablet</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={element.visibility?.mobile !== false}
                onCheckedChange={(checked) => updateVisibility({ mobile: checked })}
              />
              <Smartphone className="w-4 h-4" />
              <Label>Mobile</Label>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className="border-l-4 border-l-primary">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <GripVertical className="w-4 h-4 text-muted-foreground cursor-move" />
            <div>
              <CardTitle className="text-base">
                {getElementTypeLabel(element.type)}
              </CardTitle>
              <Badge variant="secondary" className="text-xs">
                {element.type}
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMove('up')}
            >
              <ChevronUp className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMove('down')}
            >
              <ChevronDown className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={onDelete}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content">İçerik</TabsTrigger>
              <TabsTrigger value="settings">Ayarlar</TabsTrigger>
              <TabsTrigger value="visibility">Görünürlük</TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="mt-4">
              {renderContentEditor()}
            </TabsContent>

            <TabsContent value="settings" className="mt-4">
              {renderSettingsEditor()}
            </TabsContent>

            <TabsContent value="visibility" className="mt-4">
              {renderVisibilityEditor()}
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  );
}
