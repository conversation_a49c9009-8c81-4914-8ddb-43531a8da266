import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight, Loader2 } from 'lucide-react';
import { pageService, Page } from '@/lib/firebaseService';

interface DynamicPageProps {
  slug?: string;
}

export default function DynamicPage({ slug = 'home' }: DynamicPageProps) {
  const [page, setPage] = useState<Page | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadPage();
  }, [slug]);

  const loadPage = async () => {
    setLoading(true);
    setError('');
    
    try {
      const fetchedPage = await pageService.getPageBySlug(slug);
      if (fetchedPage && fetchedPage.isPublished) {
        setPage(fetchedPage);
      } else {
        setError('<PERSON>fa bulunamadı veya yayınlanmamış');
      }
    } catch (err) {
      setError('<PERSON>fa yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error || !page) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Sayfa Bulunamadı</h1>
          <p className="text-muted-foreground mb-4">
            {error || 'Aradığınız sayfa bulunamadı.'}
          </p>
          <Button onClick={() => window.location.href = '/'}>
            Ana Sayfaya Dön
          </Button>
        </div>
      </div>
    );
  }

  const { hero } = page.content;

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      {hero && (
        <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
          <div className="container mx-auto text-center">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 max-w-4xl mx-auto leading-tight">
              {hero.title}
            </h1>
            
            {hero.subtitle && (
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                {hero.subtitle}
              </p>
            )}
            
            {hero.ctaText && hero.ctaLink && (
              <div className="flex justify-center">
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-lg px-8" asChild>
                  <a href={hero.ctaLink}>
                    {hero.ctaText}
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </a>
                </Button>
              </div>
            )}
          </div>
        </section>
      )}

      {/* Additional sections can be added here */}
      {page.content.sections && page.content.sections.length > 0 && (
        <div className="space-y-16">
          {page.content.sections.map((section) => (
            <section key={section.id} className="px-4 sm:px-6 lg:px-8">
              <div className="container mx-auto">
                {section.title && (
                  <h2 className="text-3xl font-bold mb-6 text-center">
                    {section.title}
                  </h2>
                )}
                {section.content && (
                  <div className="prose prose-lg max-w-none">
                    {section.content}
                  </div>
                )}
              </div>
            </section>
          ))}
        </div>
      )}
    </div>
  );
}
