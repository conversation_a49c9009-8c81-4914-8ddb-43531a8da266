import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Eye, Loader2 } from 'lucide-react';
import { pageService, Page, PageContent } from '@/lib/firebaseService';
import AdminAuth from '@/components/admin/AdminAuth';

export default function PageEditor() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNew = id === 'new';
  
  const [loading, setLoading] = useState(!isNew);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [formData, setFormData] = useState<Omit<Page, 'id' | 'createdAt' | 'updatedAt'>>({
    title: '',
    slug: '',
    isPublished: false,
    content: {
      hero: {
        title: '',
        subtitle: '',
        ctaText: 'Başlayın',
        ctaLink: '#'
      },
      sections: []
    }
  });

  useEffect(() => {
    if (!isNew && id) {
      loadPage(id);
    }
  }, [id, isNew]);

  const loadPage = async (pageId: string) => {
    setLoading(true);
    const page = await pageService.getPage(pageId);
    if (page) {
      setFormData({
        title: page.title,
        slug: page.slug,
        isPublished: page.isPublished,
        content: page.content
      });
    } else {
      setError('Sayfa bulunamadı');
    }
    setLoading(false);
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }));
  };

  const handleHeroChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      content: {
        ...prev.content,
        hero: {
          ...prev.content.hero,
          [field]: value
        }
      }
    }));
  };

  const handleSave = async () => {
    setError('');
    setSuccess('');
    setSaving(true);

    try {
      if (!formData.title.trim()) {
        setError('Sayfa başlığı gereklidir');
        return;
      }

      if (!formData.slug.trim()) {
        setError('Sayfa URL\'i gereklidir');
        return;
      }

      let result;
      if (isNew) {
        result = await pageService.createPage(formData);
        if (result) {
          setSuccess('Sayfa başarıyla oluşturuldu');
          setTimeout(() => {
            navigate(`/admin/pages/${result}/edit`);
          }, 1000);
        } else {
          setError('Sayfa oluşturulurken bir hata oluştu');
        }
      } else if (id) {
        result = await pageService.updatePage(id, formData);
        if (result) {
          setSuccess('Sayfa başarıyla güncellendi');
        } else {
          setError('Sayfa güncellenirken bir hata oluştu');
        }
      }
    } catch (error: any) {
      setError(error.message || 'Bir hata oluştu');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminAuth>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </AdminAuth>
    );
  }

  return (
    <AdminAuth>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" onClick={() => navigate('/admin')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Geri
            </Button>
            <div>
              <h1 className="text-2xl font-bold">
                {isNew ? 'Yeni Sayfa' : 'Sayfa Düzenle'}
              </h1>
              <p className="text-muted-foreground">
                {isNew ? 'Yeni bir sayfa oluşturun' : formData.title}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" disabled={saving}>
              <Eye className="w-4 h-4 mr-2" />
              Önizle
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Kaydet
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main content */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="content" className="space-y-4">
              <TabsList>
                <TabsTrigger value="content">İçerik</TabsTrigger>
                <TabsTrigger value="seo">SEO</TabsTrigger>
              </TabsList>
              
              <TabsContent value="content" className="space-y-4">
                {/* Hero Section */}
                <Card>
                  <CardHeader>
                    <CardTitle>Hero Bölümü</CardTitle>
                    <CardDescription>
                      Sayfanın üst kısmında görünecek ana içerik
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="hero-title">Ana Başlık</Label>
                      <Input
                        id="hero-title"
                        value={formData.content.hero?.title || ''}
                        onChange={(e) => handleHeroChange('title', e.target.value)}
                        placeholder="Sayfanın ana başlığı"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="hero-subtitle">Alt Başlık</Label>
                      <Textarea
                        id="hero-subtitle"
                        value={formData.content.hero?.subtitle || ''}
                        onChange={(e) => handleHeroChange('subtitle', e.target.value)}
                        placeholder="Sayfanın açıklaması"
                        rows={3}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="hero-cta-text">Buton Metni</Label>
                        <Input
                          id="hero-cta-text"
                          value={formData.content.hero?.ctaText || ''}
                          onChange={(e) => handleHeroChange('ctaText', e.target.value)}
                          placeholder="Başlayın"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="hero-cta-link">Buton Linki</Label>
                        <Input
                          id="hero-cta-link"
                          value={formData.content.hero?.ctaLink || ''}
                          onChange={(e) => handleHeroChange('ctaLink', e.target.value)}
                          placeholder="#"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="hero-image">Arka Plan Görseli</Label>
                      <Input
                        id="hero-image"
                        value={formData.content.hero?.backgroundImage || ''}
                        onChange={(e) => handleHeroChange('backgroundImage', e.target.value)}
                        placeholder="https://example.com/hero-image.jpg"
                      />
                      {formData.content.hero?.backgroundImage && (
                        <img
                          src={formData.content.hero.backgroundImage}
                          alt="Hero background"
                          className="w-full h-32 object-cover rounded-lg border mt-2"
                        />
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Content Sections */}
                <Card>
                  <CardHeader>
                    <CardTitle>İçerik Bölümleri</CardTitle>
                    <CardDescription>
                      Sayfa içerisindeki farklı bölümleri düzenleyin
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* About Section */}
                    <div className="border rounded-lg p-4">
                      <h3 className="font-semibold mb-3">Hakkımızda Bölümü</h3>
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="about-title">Başlık</Label>
                          <Input
                            id="about-title"
                            placeholder="Hakkımızda başlığı"
                          />
                        </div>
                        <div>
                          <Label htmlFor="about-content">İçerik</Label>
                          <Textarea
                            id="about-content"
                            placeholder="Hakkımızda metni"
                            rows={4}
                          />
                        </div>
                        <div>
                          <Label htmlFor="about-image">Görsel URL</Label>
                          <Input
                            id="about-image"
                            placeholder="https://example.com/about-image.jpg"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Features Section */}
                    <div className="border rounded-lg p-4">
                      <h3 className="font-semibold mb-3">Özellikler Bölümü</h3>
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="features-title">Başlık</Label>
                          <Input
                            id="features-title"
                            placeholder="Özellikler başlığı"
                          />
                        </div>
                        <div>
                          <Label htmlFor="features-subtitle">Alt Başlık</Label>
                          <Input
                            id="features-subtitle"
                            placeholder="Özellikler alt başlığı"
                          />
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Not: Özellik kartları ayrı bir bölümde düzenlenecek
                        </p>
                      </div>
                    </div>

                    {/* Contact Section */}
                    <div className="border rounded-lg p-4">
                      <h3 className="font-semibold mb-3">İletişim Bölümü</h3>
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <Label htmlFor="contact-phone">Telefon</Label>
                            <Input
                              id="contact-phone"
                              placeholder="+90 (*************"
                            />
                          </div>
                          <div>
                            <Label htmlFor="contact-email">E-posta</Label>
                            <Input
                              id="contact-email"
                              placeholder="<EMAIL>"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="contact-address">Adres</Label>
                          <Textarea
                            id="contact-address"
                            placeholder="Tam adres bilgisi"
                            rows={2}
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="seo">
                <Card>
                  <CardHeader>
                    <CardTitle>SEO Ayarları</CardTitle>
                    <CardDescription>
                      Arama motorları için optimizasyon ayarları
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      SEO ayarları yakında eklenecek...
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Page Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Sayfa Ayarları</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Sayfa Başlığı *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    placeholder="Sayfa başlığı"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    placeholder="sayfa-url"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Sayfa URL'i: /{formData.slug}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="published"
                    checked={formData.isPublished}
                    onCheckedChange={(checked) => 
                      setFormData(prev => ({ ...prev, isPublished: checked }))
                    }
                  />
                  <Label htmlFor="published">Yayınlanmış</Label>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Hızlı İşlemler</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  Önizleme
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full" 
                  size="sm"
                  onClick={handleSave}
                  disabled={saving}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Taslak Kaydet
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminAuth>
  );
}
