import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Package, Upload, Loader2 } from 'lucide-react';
import AdminAuth from '@/components/admin/AdminAuth';

interface ProductFormData {
  name: string;
  category: string;
  brand: string;
  price: string;
  unit: string;
  description: string;
  image: string;
  inStock: boolean;
}

export default function ProductEditor() {
  const { id } = useParams();
  const navigate = useNavigate();
  const isNew = id === 'new';
  
  const [loading, setLoading] = useState(!isNew);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    category: '',
    brand: '',
    price: '',
    unit: '',
    description: '',
    image: '',
    inStock: true
  });

  const categories = [
    'Peynir Ürünleri',
    'Soslar',
    'Et Ürünleri',
    'Süt Ürünleri',
    'Meyve & Sebze',
    'Kuru Gıdalar',
    'İçecekler',
    'Donuk Ürünler',
    'Temizlik Ürünleri'
  ];

  const brands = [
    'Bahçıvan',
    'TAT',
    'HORECA',
    'Pınar',
    'Ülker',
    'Eti',
    'Torku',
    'Diğer'
  ];

  const units = [
    '100g', '200g', '250g', '300g', '400g', '420g', '500g', '900g', '1kg',
    '100ml', '200ml', '250ml', '500ml', '1L', '2L',
    'Adet', 'Paket', 'Kutu', 'Şişe', 'Poşet'
  ];

  useEffect(() => {
    if (!isNew && id) {
      loadProduct(id);
    }
  }, [id, isNew]);

  const loadProduct = async (productId: string) => {
    setLoading(true);
    // Mock data loading - replace with actual API call
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock product data
      if (productId === '1') {
        setFormData({
          name: 'Bahçıvan Tam Yağlı Beyaz Peynir',
          category: 'Peynir Ürünleri',
          brand: 'Bahçıvan',
          price: '125.50',
          unit: '900g',
          description: 'Pastörize inek sütünden üretilen, porselen beyazı renkte ve kremsi yapıda beyaz peynir',
          image: 'https://images.unsplash.com/photo-1586511925558-a4c6376fe65f?w=300&h=300&fit=crop',
          inStock: true
        });
      }
    } catch (error) {
      setError('Ürün yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setError('');
    setSuccess('');
    setSaving(true);

    try {
      // Validation
      if (!formData.name.trim()) {
        setError('Ürün adı gereklidir');
        return;
      }
      if (!formData.category) {
        setError('Kategori seçimi gereklidir');
        return;
      }
      if (!formData.brand) {
        setError('Marka seçimi gereklidir');
        return;
      }
      if (!formData.price.trim()) {
        setError('Fiyat gereklidir');
        return;
      }

      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (isNew) {
        setSuccess('Ürün başarıyla oluşturuldu');
        setTimeout(() => {
          navigate('/admin');
        }, 1500);
      } else {
        setSuccess('Ürün başarıyla güncellendi');
      }
    } catch (error: any) {
      setError(error.message || 'Bir hata oluştu');
    } finally {
      setSaving(false);
    }
  };

  const handleImageUpload = () => {
    // Mock image upload
    const imageUrl = `https://images.unsplash.com/photo-${Date.now()}?w=300&h=300&fit=crop`;
    handleInputChange('image', imageUrl);
  };

  if (loading) {
    return (
      <AdminAuth>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </AdminAuth>
    );
  }

  return (
    <AdminAuth>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" onClick={() => navigate('/admin')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Geri
            </Button>
            <div>
              <h1 className="text-2xl font-bold">
                {isNew ? 'Yeni Ürün' : 'Ürün Düzenle'}
              </h1>
              <p className="text-muted-foreground">
                {isNew ? 'Yeni bir ürün oluşturun' : formData.name}
              </p>
            </div>
          </div>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Kaydediliyor...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Kaydet
              </>
            )}
          </Button>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Temel Bilgiler</CardTitle>
                <CardDescription>
                  Ürünün temel bilgilerini girin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Ürün Adı *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Ürün adını girin"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Kategori *</Label>
                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Kategori seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="brand">Marka *</Label>
                    <Select value={formData.brand} onValueChange={(value) => handleInputChange('brand', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Marka seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {brands.map((brand) => (
                          <SelectItem key={brand} value={brand}>
                            {brand}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Açıklama</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Ürün açıklamasını girin"
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Pricing & Inventory */}
            <Card>
              <CardHeader>
                <CardTitle>Fiyat ve Stok</CardTitle>
                <CardDescription>
                  Fiyat ve stok bilgilerini girin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Fiyat (₺) *</Label>
                    <Input
                      id="price"
                      value={formData.price}
                      onChange={(e) => handleInputChange('price', e.target.value)}
                      placeholder="99.99"
                      type="number"
                      step="0.01"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="unit">Birim</Label>
                    <Select value={formData.unit} onValueChange={(value) => handleInputChange('unit', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Birim seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        {units.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {unit}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="inStock"
                    checked={formData.inStock}
                    onCheckedChange={(checked) => handleInputChange('inStock', checked)}
                  />
                  <Label htmlFor="inStock">Stokta mevcut</Label>
                </div>
              </CardContent>
            </Card>

            {/* Product Image */}
            <Card>
              <CardHeader>
                <CardTitle>Ürün Görseli</CardTitle>
                <CardDescription>
                  Ürün için görsel yükleyin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.image && (
                  <div className="w-full max-w-xs">
                    <img
                      src={formData.image}
                      alt="Ürün görseli"
                      className="w-full h-48 object-cover rounded-lg border"
                    />
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="image">Görsel URL</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="image"
                      value={formData.image}
                      onChange={(e) => handleInputChange('image', e.target.value)}
                      placeholder="https://example.com/image.jpg"
                    />
                    <Button type="button" variant="outline" onClick={handleImageUpload}>
                      <Upload className="w-4 h-4 mr-2" />
                      Yükle
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Hızlı İşlemler</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full" 
                  size="sm"
                  onClick={handleSave}
                  disabled={saving}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Kaydet
                </Button>
                
                <Button 
                  variant="outline" 
                  className="w-full" 
                  size="sm"
                >
                  <Package className="w-4 h-4 mr-2" />
                  Önizleme
                </Button>
              </CardContent>
            </Card>

            {/* Product Status */}
            <Card>
              <CardHeader>
                <CardTitle>Ürün Durumu</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Durum:</span>
                  <span className={`text-sm ${formData.inStock ? 'text-green-600' : 'text-red-600'}`}>
                    {formData.inStock ? 'Stokta' : 'Stok Yok'}
                  </span>
                </div>
                
                {formData.category && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Kategori:</span>
                    <span className="text-sm">{formData.category}</span>
                  </div>
                )}
                
                {formData.brand && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Marka:</span>
                    <span className="text-sm">{formData.brand}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AdminAuth>
  );
}
