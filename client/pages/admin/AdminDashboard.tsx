import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  MoreHorizontal,
  Package,
  ShoppingCart,
  Settings,
  Image,
  Type,
  Users,
  TrendingUp
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { pageService, Page } from '@/lib/firebaseService';
import AdminAuth from '@/components/admin/AdminAuth';

// Mock product data - will be replaced with Firebase
const mockProducts = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON> Beyaz Peynir",
    category: "Peynir Ürünleri",
    brand: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    price: "₺125.50",
    unit: "900g",
    image: "https://images.unsplash.com/photo-1586511925558-a4c6376fe65f?w=300&h=300&fit=crop",
    inStock: true,
    createdAt: new Date()
  },
  {
    id: 2,
    name: "TAT Ketçap",
    category: "Soslar",
    brand: "TAT",
    price: "₺24.90",
    unit: "420g",
    image: "https://images.unsplash.com/photo-1578454163618-1444ad4b5e82?w=300&h=300&fit=crop",
    inStock: true,
    createdAt: new Date()
  }
];

export default function AdminDashboard() {
  const [pages, setPages] = useState<Page[]>([]);
  const [products, setProducts] = useState(mockProducts);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPages();
  }, []);

  const loadPages = async () => {
    setLoading(true);
    try {
      const timeoutPromise = new Promise<Page[]>((_, reject) => 
        setTimeout(() => reject(new Error('Load timeout')), 3000)
      );
      
      const pagesPromise = pageService.getPages();
      
      const fetchedPages = await Promise.race([pagesPromise, timeoutPromise]);
      setPages(fetchedPages);
    } catch (error) {
      console.error('Error loading pages:', error);
      setPages([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePage = async (id: string) => {
    if (window.confirm('Bu sayfayı silmek istediğinizden emin misiniz?')) {
      const success = await pageService.deletePage(id);
      if (success) {
        await loadPages();
      }
    }
  };

  const handleTogglePublish = async (page: Page) => {
    if (page.id) {
      const success = await pageService.updatePage(page.id, {
        isPublished: !page.isPublished
      });
      if (success) {
        await loadPages();
      }
    }
  };

  const handleDeleteProduct = (id: number) => {
    if (window.confirm('Bu ürünü silmek istediğinizden emin misiniz?')) {
      setProducts(prev => prev.filter(p => p.id !== id));
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Bilinmiyor';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const totalProducts = products.length;
  const inStockProducts = products.filter(p => p.inStock).length;
  const totalPages = pages.length;
  const publishedPages = pages.filter(p => p.isPublished).length;

  return (
    <AdminAuth>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Yönetim Paneli</h1>
            <p className="text-muted-foreground">
              Website içeriklerinizi ve ürünlerinizi yönetin
            </p>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Sayfa</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalPages}</div>
              <p className="text-xs text-muted-foreground">
                {publishedPages} yayınlanan
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Ürün</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalProducts}</div>
              <p className="text-xs text-muted-foreground">
                {inStockProducts} stokta
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Kategoriler</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">6</div>
              <p className="text-xs text-muted-foreground">
                Aktif kategori
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Markalar</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">5</div>
              <p className="text-xs text-muted-foreground">
                Farklı marka
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Content Management Tabs */}
        <Tabs defaultValue="pages" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="pages">Sayfalar</TabsTrigger>
            <TabsTrigger value="products">Ürünler</TabsTrigger>
            <TabsTrigger value="content">İçerik</TabsTrigger>
            <TabsTrigger value="media">Medya</TabsTrigger>
          </TabsList>
          
          {/* Pages Tab */}
          <TabsContent value="pages" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Sayfa Yönetimi</h2>
              <Link to="/admin/pages/new">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Yeni Sayfa
                </Button>
              </Link>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Sayfalar</CardTitle>
                <CardDescription>
                  Website sayfalarınızı düzenleyin ve yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">Yükleniyor...</div>
                ) : pages.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Henüz sayfa yok</h3>
                    <p className="text-muted-foreground mb-4">
                      İlk sayfanızı oluşturmak için başlayın
                    </p>
                    <Link to="/admin/pages/new">
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Yeni Sayfa Oluştur
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pages.map((page) => (
                      <div
                        key={page.id}
                        className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-semibold">{page.title}</h3>
                            <Badge variant={page.isPublished ? "default" : "secondary"}>
                              {page.isPublished ? "Yayınlandı" : "Taslak"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Slug: /{page.slug}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Son güncelleme: {formatDate(page.updatedAt)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm" asChild>
                            <Link to={`/admin/pages/${page.id}/edit`}>
                              <Edit className="w-4 h-4" />
                            </Link>
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem
                                onClick={() => handleTogglePublish(page)}
                              >
                                {page.isPublished ? (
                                  <>
                                    <EyeOff className="w-4 h-4 mr-2" />
                                    Yayından Kaldır
                                  </>
                                ) : (
                                  <>
                                    <Eye className="w-4 h-4 mr-2" />
                                    Yayınla
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => page.id && handleDeletePage(page.id)}
                                className="text-destructive"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Sil
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Products Tab */}
          <TabsContent value="products" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Ürün Yönetimi</h2>
              <Link to="/admin/products/new">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Yeni Ürün
                </Button>
              </Link>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Ürünler</CardTitle>
                <CardDescription>
                  Ürün kataloğunuzu yönetin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {products.map((product) => (
                    <div
                      key={product.id}
                      className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center space-x-4 flex-1">
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-semibold">{product.name}</h3>
                            <Badge variant="outline">{product.brand}</Badge>
                            <Badge variant={product.inStock ? "default" : "destructive"}>
                              {product.inStock ? "Stokta" : "Stok Yok"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Kategori: {product.category} | Fiyat: {product.price} | Birim: {product.unit}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link to={`/admin/products/${product.id}/edit`}>
                            <Edit className="w-4 h-4" />
                          </Link>
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem
                              onClick={() => handleDeleteProduct(product.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Sil
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-4">
            <h2 className="text-xl font-semibold">İçerik Yönetimi</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Type className="w-5 h-5 mr-2" />
                    Ana Sayfa İçeriği
                  </CardTitle>
                  <CardDescription>
                    Slider, kategoriler ve diğer ana sayfa içerikleri
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Link to="/admin/homepage">
                    <Button variant="outline" className="w-full">
                      <Edit className="w-4 h-4 mr-2" />
                      Düzenle
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings className="w-5 h-5 mr-2" />
                    Gelişmiş Sayfa Editörü
                  </CardTitle>
                  <CardDescription>
                    Drag & drop ile dinamik sayfa oluşturma
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Link to="/admin/pages/advanced/new">
                    <Button variant="outline" className="w-full">
                      <Plus className="w-4 h-4 mr-2" />
                      Yeni Sayfa
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="w-5 h-5 mr-2" />
                    Hakkımızda İçeriği
                  </CardTitle>
                  <CardDescription>
                    Şirket bilgileri, tarihçe ve ekip
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    <Edit className="w-4 h-4 mr-2" />
                    Düzenle
                  </Button>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings className="w-5 h-5 mr-2" />
                    İletişim Bilgileri
                  </CardTitle>
                  <CardDescription>
                    Telefon, adres ve diğer iletişim bilgileri
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    <Edit className="w-4 h-4 mr-2" />
                    Düzenle
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Media Tab */}
          <TabsContent value="media" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Medya Yönetimi</h2>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Medya Yükle
              </Button>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Medya Kütüphanesi</CardTitle>
                <CardDescription>
                  Resimler, videolar ve diğer medya dosyaları
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Image className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Medya kütüphanesi boş</h3>
                  <p className="text-muted-foreground mb-4">
                    İlk medya dosyanızı yükleyin
                  </p>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Medya Yükle
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminAuth>
  );
}
