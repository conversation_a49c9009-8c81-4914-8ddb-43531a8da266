{% comment %}
  Renders product variant options
  Accepts:
  - product: {Object} product object.
  - option: {Object} current product_option object.
  - block: {Object} block object.
  Usage:
  {% render 'product-variant-options',
    product: product,
    option: option,
    block: block,
    show_color_swatch: boolean
  %}
{% endcomment %}
{%- liquid
  assign variants_available_arr = product.variants | map: 'available'
  assign product_form_id = 'product-form-' | append: section.id
-%}

{%- assign variant_swatches = product.metafields.custom.variant_swatch_map_ovveride | parse_json -%}

{%- for value in option.values -%}
  {%- liquid
    assign option_disabled = true

    for variant in product.variants
      assign match = true

      for i in (1..option.position)
        assign index = i | minus: 1
        assign selected_value = product.selected_or_first_available_variant.options[index]
        assign variant_value = variant.options[index]

        if i == option.position
          if variant_value != value
            assign match = false
          endif
        else
          if selected_value != variant_value
            assign match = false
          endif
        endif
      endfor

      if match and variant.available
        assign option_disabled = false
      endif
    endfor
  -%}

  {%- assign swatch_image = '' -%}

  {%- if variant_swatches != blank -%}
    {%- for swatch in variant_swatches -%}
      {%- if swatch.variant_value == value -%}
        {%- assign swatch_image = swatch.variant_swatch -%}
      {%- endif -%}
    {%- endfor -%}
  {%- endif -%}

  {% if show_color_swatch %}

    {% liquid
      assign color_value = ''

      if swatch_image != ''
        assign color_value = 'url(' | append: swatch_image | append: ') no-repeat center/cover'
      else
        assign init_color_value = value | handle | strip
        assign color_value = init_color_value | remove: '-'
      endif

      assign variant_link = product.url
      assign variant_id = ''

      for variant in product.variants
        assign flag = false
        if variant.available
          for option_variant in variant.options
            if option_variant == value
              assign flag = true
              break
            endif
          endfor
        endif

        if flag == true
          assign variant_link = variant.url
          assign variant_id = variant.id
          break
        endif
      endfor
    %}

    <input
      type="radio"
      id="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}-{{ product.id }}"
      name="{{ option.name }}-{{ section.id }}-{{ product.id }}"
      value="{{ value | escape }}"
      {% if variant_link %}
        data-variant-link="{{ variant_link }}"
      {% endif %}
      {% if variant_id %}
        data-variant-id="{{ variant_id }}"
      {% endif %}
      {% if no_form_id != true %}
        form="{{ product_form_id }}"
      {% endif %}
      {% if option.selected_value == value %}
        checked
      {% endif %}
      {% if option_disabled and no_form_id != true %}
        class="disabled"
      {% endif %}
    >
    <label
      for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}-{{ product.id }}"
      class="color-swatch"
      title="{{ value | escape }}"
      style="--swatch-color: {{ color_value }}; border-radius: {{ settings.swatch_border_radius }}px"
    >
      <span class="visually-hidden">{{ value -}}</span>

      {%- if swatch_image != '' -%}
        <img
          src="{{ swatch_image | file_url }}"
          alt="{{ value }}"
          style="width:50px; height:auto; margin-top:5px; border-radius:4px; border:1px solid #ddd;"
        >
      {%- endif -%}

    </label>

  {% else %}

    {%- if block.settings.picker_type == 'button' -%}
      {%- if forloop.length > 10 -%}
        <input
          type="radio"
          id="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
          name="{{ option.name }}"
          value="{{ value | escape }}"
          {% if no_form_id != true %}
            form="{{ product_form_id }}"
          {% endif %}
          {% if option.selected_value == value %}
            checked
          {% endif %}
          class="{% if option_disabled %}disabled{% endif %} {% if forloop.index > 4 %}hidden{% endif %}"
        >
        <label
          for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
          class="{% if forloop.index > 4 %}hidden{% endif %}"
        >
          {{ value -}}
          <span class="visually-hidden">{{ 'products.product.variant_sold_out_or_unavailable' | t }}</span>
        </label>

        {% comment %}
          
        {% endcomment %}
        {% if forloop.index == forloop.length %}
          <a href="javascript:;" class="product-more product-more--offset js-show-more link-hover-line">
            {{ 'products.product.show_more' | t }}
          </a>
        {% endif %} 

      {%- else -%}

        <input
          type="radio"
          id="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
          name="{{ option.name }}"
          value="{{ value | escape }}"
          {% if no_form_id != true %}
            form="{{ product_form_id }}"
          {% endif %}
          {% if option.selected_value == value %}
            checked
          {% endif %}
          {% if option_disabled %}
            class="disabled"
          {% endif %}
        >
        <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}">
          {{ value -}}
          <span class="visually-hidden">{{ 'products.product.variant_sold_out_or_unavailable' | t }}</span>
        </label>

      {%- endif -%}

    {%- elsif block.settings.picker_type == 'dropdown' -%}

      <option 
        value="{{ value | escape }}"
        {% if option.selected_value == value %}
          selected="selected"
        {% endif %}
      >
        {% if option_disabled -%}
          {{- 'products.product.value_unavailable' | t: option_value: value -}}
        {%- else -%}
          {{- value -}}
        {%- endif %}
      </option>

    {% endif %}

  {% endif %}

{%- endfor -%}
