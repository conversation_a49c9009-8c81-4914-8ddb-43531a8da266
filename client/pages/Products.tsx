import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  Filter, 
  Heart,
  ShoppingCart,
  Star,
  Package
} from 'lucide-react';

export default function Products() {
  const [selectedCategory, setSelectedCategory] = useState('Tümü');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = ['Tümü', 'Peynir Ürünleri', 'Soslar', 'Et Ürünleri', '<PERSON>üt Ürünleri', 'Meyve & Sebze', 'Kuru Gıdalar'];

  const products = [
    // Peynir Ürünleri
    {
      id: 1,
      name: "Bahçıvan Tam Yağlı Beyaz Peynir",
      category: "Peynir Ürünleri",
      brand: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      price: "₺125.50",
      unit: "900g",
      image: "https://images.unsplash.com/photo-1586511925558-a4c6376fe65f?w=300&h=300&fit=crop",
      description: "Pastörize inek sütünden üretilen, porselen beyazı renkte ve kremsi yapıda beyaz peynir",
      inStock: true,
      rating: 4.8
    },
    {
      id: 2,
      name: "Bahçıvan Taze Kaşar Peynir",
      category: "Peynir Ürünleri",
      brand: "Bahçıvan",
      price: "₺189.99",
      unit: "400g",
      image: "https://images.unsplash.com/photo-1552767059-ce182ead6c1b?w=300&h=300&fit=crop",
      description: "Özel olgunlaştırma odasında bekletilen, kendine has tat ve aromaya sahip kaşar peynir",
      inStock: true,
      rating: 4.7
    },
    {
      id: 3,
      name: "Bahçıvan Serpme Beyaz Peynir",
      category: "Peynir Ürünleri",
      brand: "Bahçıvan",
      price: "₺45.75",
      unit: "200g",
      image: "https://images.unsplash.com/photo-1559561853-08451507cbe7?w=300&h=300&fit=crop",
      description: "Türkiye'de ilk ve tek, pratik kapağı ile küp formunda beyaz peynir",
      inStock: true,
      rating: 4.6
    },
    {
      id: 4,
      name: "Bahçıvan Light Laktozsuz Beyaz Peynir",
      category: "Peynir Ürünleri",
      brand: "Bahçıvan",
      price: "₺95.00",
      unit: "420g",
      image: "https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=300&h=300&fit=crop",
      description: "Laktoz intoleransı olanlar için, yağı ve tuzu azaltılmış dilimli beyaz peynir",
      inStock: true,
      rating: 4.5
    },
    // Soslar
    {
      id: 5,
      name: "TAT Ketçap",
      category: "Soslar",
      brand: "TAT",
      price: "₺24.90",
      unit: "420g",
      image: "https://images.unsplash.com/photo-1578454163618-1444ad4b5e82?w=300&h=300&fit=crop",
      description: "Geleneksel lezzet, doğal domates tadı ile klasik ketçap",
      inStock: true,
      rating: 4.8
    },
    {
      id: 6,
      name: "TAT Sarımsaklı Mayonez",
      category: "Soslar",
      brand: "TAT",
      price: "₺32.50",
      unit: "350g",
      image: "https://images.unsplash.com/photo-1516685018646-549198525c1b?w=300&h=300&fit=crop",
      description: "Klasik mayonez lezzetini sarımsak aromasıyla birleştiren özel sos",
      inStock: true,
      rating: 4.7
    },
    {
      id: 7,
      name: "TAT Acı Sos",
      category: "Soslar",
      brand: "TAT",
      price: "₺18.75",
      unit: "230g",
      image: "https://images.unsplash.com/photo-1544145945-f90425340c7e?w=300&h=300&fit=crop",
      description: "Yemeklere sıra dışı keyif veren doğal ve katkısız acı sos",
      inStock: true,
      rating: 4.6
    },
    {
      id: 8,
      name: "TAT Burger Sos",
      category: "Soslar",
      brand: "TAT",
      price: "₺28.90",
      unit: "230g",
      image: "https://images.unsplash.com/photo-1551782450-17144efb9c50?w=300&h=300&fit=crop",
      description: "Burger tutkunları için özel olarak geliştirilmiş nefis burger sosu",
      inStock: true,
      rating: 4.9
    },
    // Et Ürünleri
    {
      id: 9,
      name: "Premium Dana Kıyma",
      category: "Et Ürünleri",
      brand: "Horeca",
      price: "₺189.99",
      unit: "500g",
      image: "https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=300&h=300&fit=crop",
      description: "Taze dana etinden öğütülmüş, hijyenik koşullarda hazırlanmış kıyma",
      inStock: true,
      rating: 4.8
    },
    {
      id: 10,
      name: "Tavuk But",
      category: "Et Ürünleri",
      brand: "Horeca",
      price: "₺45.90",
      unit: "1kg",
      image: "https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=300&h=300&fit=crop",
      description: "Taze tavuk budu, restoran kalitesinde hazırlanmış",
      inStock: true,
      rating: 4.7
    },
    // Süt Ürünleri
    {
      id: 11,
      name: "Tam Yağlı Süt",
      category: "Süt Ürünleri",
      brand: "Horeca",
      price: "₺18.50",
      unit: "1L",
      image: "https://images.unsplash.com/photo-1550583724-b2692b85b150?w=300&h=300&fit=crop",
      description: "Pastörize tam yağlı inek sütü, soğuk zincirle teslim",
      inStock: true,
      rating: 4.6
    },
    {
      id: 12,
      name: "Çilek Aromalı Yoğurt",
      category: "Süt Ürünleri",
      brand: "Horeca",
      price: "₺12.90",
      unit: "150g",
      image: "https://images.unsplash.com/photo-1571212515416-94c2f6a04d97?w=300&h=300&fit=crop",
      description: "Doğal çilek aroması ile hazırlanmış kremsi yoğurt",
      inStock: true,
      rating: 4.5
    }
  ];

  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory === 'Tümü' || product.category === selectedCategory;
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">Ürünlerimiz</h1>
          <p className="text-xl text-muted-foreground">
            Kaliteli gıda ve içecek ürünlerimizi keşfedin
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Ürün adı veya marka ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="md:w-auto">
              <Filter className="w-4 h-4 mr-2" />
              Filtrele
            </Button>
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Badge
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                className="cursor-pointer px-4 py-2"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Badge>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="group hover:shadow-lg transition-all duration-300">
              <div className="relative overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-2 right-2 flex space-x-1">
                  <Button size="sm" variant="secondary" className="w-8 h-8 p-0">
                    <Heart className="w-4 h-4" />
                  </Button>
                </div>
                {!product.inStock && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <span className="text-white font-semibold">Stokta Yok</span>
                  </div>
                )}
              </div>
              
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between mb-1">
                  <Badge variant="outline" className="text-xs">
                    {product.brand}
                  </Badge>
                  <div className="flex items-center space-x-1">
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs text-muted-foreground">{product.rating}</span>
                  </div>
                </div>
                <CardTitle className="text-lg line-clamp-2">{product.name}</CardTitle>
                <CardDescription className="text-sm line-clamp-2">
                  {product.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <div className="text-lg font-bold text-primary">{product.price}</div>
                    <div className="text-xs text-muted-foreground">{product.unit}</div>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    <Package className="w-3 h-3 mr-1" />
                    {product.unit}
                  </Badge>
                </div>
                
                <Button 
                  className="w-full" 
                  disabled={!product.inStock}
                  size="sm"
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  {product.inStock ? 'Sepete Ekle' : 'Stokta Yok'}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-16">
            <Package className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Ürün bulunamadı</h3>
            <p className="text-muted-foreground">
              Arama kriterlerinizi değiştirerek tekrar deneyin.
            </p>
          </div>
        )}

        {/* Load More */}
        {filteredProducts.length > 0 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Daha Fazla Ürün Yükle
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
