import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Image as ImageIcon, 
  ZoomIn, 
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

export default function Gallery() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('Tümü');

  // Örnek galeri resimleri - gerçek projede bu veriler API'den gelecek
  const galleryImages = [
    {
      id: 1,
      src: "https://images.unsplash.com/photo-1542838132-92c53300491e?w=800&h=600&fit=crop",
      title: "Modern Depo Tesisleri",
      category: "Tesisler",
      description: "3.000 m² kapalı depo alanımız"
    },
    {
      id: 2,
      src: "https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=800&h=600&fit=crop",
      title: "Lojistik Merkezi",
      category: "Tesisler",
      description: "Modern lojistik ve dağıtım merkezi"
    },
    {
      id: 3,
      src: "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=800&h=600&fit=crop",
      title: "Gıda Ürünleri",
      category: "Ürünler",
      description: "Kaliteli gıda ürün yelpazesi"
    },
    {
      id: 4,
      src: "https://images.unsplash.com/photo-1550583724-b2692b85b150?w=800&h=600&fit=crop",
      title: "Süt Ürünleri",
      category: "Ürünler",
      description: "Taze süt ve süt ürünleri"
    },
    {
      id: 5,
      src: "https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=800&h=600&fit=crop",
      title: "Et Ürünleri",
      category: "Ürünler",
      description: "Kaliteli et ve şarküteri ürünleri"
    },
    {
      id: 6,
      src: "https://images.unsplash.com/photo-1506976785307-8732e854ad03?w=800&h=600&fit=crop",
      title: "Meyve & Sebze",
      category: "Ürünler",
      description: "Taze meyve ve sebze çeşitleri"
    },
    {
      id: 7,
      src: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=800&h=600&fit=crop",
      title: "Deniz Ürünleri",
      category: "Ürünler",
      description: "Taze balık ve deniz ürünleri"
    },
    {
      id: 8,
      src: "https://images.unsplash.com/photo-1574484284002-952d92456975?w=800&h=600&fit=crop",
      title: "Ekip Çalışması",
      category: "Ekip",
      description: "Profesyonel ekibimiz"
    }
  ];

  const categories = ['Tümü', 'Tesisler', 'Ürünler', 'Ekip'];

  const filteredImages = selectedCategory === 'Tümü' 
    ? galleryImages 
    : galleryImages.filter(img => img.category === selectedCategory);

  const openLightbox = (imageSrc: string) => {
    setSelectedImage(imageSrc);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const currentImageIndex = selectedImage 
    ? galleryImages.findIndex(img => img.src === selectedImage)
    : -1;

  const nextImage = () => {
    if (currentImageIndex < galleryImages.length - 1) {
      setSelectedImage(galleryImages[currentImageIndex + 1].src);
    }
  };

  const prevImage = () => {
    if (currentImageIndex > 0) {
      setSelectedImage(galleryImages[currentImageIndex - 1].src);
    }
  };

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary/10 to-primary/5 py-16">
        <div className="container mx-auto px-4 text-center">
          <Badge variant="outline" className="mb-4">Galeri</Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Fotoğraf Galerimiz
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Tesislerimiz, ürünlerimiz ve ekibimizden kareler
          </p>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>

          {/* Image Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredImages.map((image) => (
              <Card 
                key={image.id} 
                className="group cursor-pointer overflow-hidden hover:shadow-lg transition-all duration-300"
                onClick={() => openLightbox(image.src)}
              >
                <CardContent className="p-0 relative">
                  <div className="aspect-square overflow-hidden">
                    <img
                      src={image.src}
                      alt={image.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors duration-300 flex items-center justify-center">
                    <ZoomIn className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <div className="p-4">
                    <Badge variant="secondary" className="mb-2 text-xs">
                      {image.category}
                    </Badge>
                    <h3 className="font-semibold mb-1">{image.title}</h3>
                    <p className="text-sm text-muted-foreground">{image.description}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Lightbox Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <img
              src={selectedImage}
              alt="Büyük görünüm"
              className="max-w-full max-h-full object-contain"
            />
            
            {/* Close Button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 text-white hover:bg-white/20"
              onClick={closeLightbox}
            >
              <X className="w-6 h-6" />
            </Button>

            {/* Navigation Buttons */}
            {currentImageIndex > 0 && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                onClick={prevImage}
              >
                <ChevronLeft className="w-6 h-6" />
              </Button>
            )}

            {currentImageIndex < galleryImages.length - 1 && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                onClick={nextImage}
              >
                <ChevronRight className="w-6 h-6" />
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
