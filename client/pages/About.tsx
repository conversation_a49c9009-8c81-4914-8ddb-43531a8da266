import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Award, 
  Users, 
  Globe, 
  Factory, 
  Truck, 
  Shield,
  Calendar,
  Target,
  Heart,
  ArrowRight
} from 'lucide-react';

export default function About() {
  const milestones = [
    {
      year: "2003",
      title: "Kuruluş",
      description: "HORECA Gıda Toptancısı olarak sektöre adım attık"
    },
    {
      year: "2008",
      title: "İlk Büyük Genişleme",
      description: "3.000 m² kapalı alana sahip modern tesisimizi açtık"
    },
    {
      year: "2012",
      title: "<PERSON>te Sertifikaları",
      description: "ISO 22000 ve HACCP kalite belgeleri aldık"
    },
    {
      year: "2018",
      title: "Dijital Dönüşüm",
      description: "Online sipariş sistemi ve e-ticaret platformunu hayata geçirdik"
    },
    {
      year: "2023",
      title: "<PERSON>ugün",
      description: "81 ile hizmet veren, 5000+ ürün çeşidi ile Türkiye'nin güvenilir tedarikçisi"
    }
  ];

  const values = [
    {
      icon: Shield,
      title: "Kalite",
      description: "Tüm ürünlerimizde uluslararası kalite standartlarını uyguluyoruz"
    },
    {
      icon: Heart,
      title: "Güven",
      description: "20 yıllık deneyimimizle müşterilerimizin güvenini kazandık"
    },
    {
      icon: Target,
      title: "İnovasyon",
      description: "Sektördeki yenilikleri takip ederek hizmet kalitemizi artırıyoruz"
    },
    {
      icon: Users,
      title: "Müşteri Memnuniyeti",
      description: "Müşteri memnuniyeti bizim için her şeyden önce gelir"
    }
  ];

  const team = [
    {
      name: "Ahmet Yılmaz",
      position: "Genel Müdür",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop",
      description: "20 yıllık gıda sektörü deneyimi"
    },
    {
      name: "Fatma Demir",
      position: "Operasyon Müdürü",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop",
      description: "Lojistik ve operasyon uzmanı"
    },
    {
      name: "Mehmet Kaya",
      position: "Kalite Kontrol Müdürü",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop",
      description: "Gıda mühendisi ve kalite uzmanı"
    },
    {
      name: "Ayşe Özkan",
      position: "Satış Müdürü",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop",
      description: "Müşteri ilişkileri ve satış uzmanı"
    }
  ];

  const stats = [
    { icon: Calendar, label: "Yıllık Deneyim", value: "20+" },
    { icon: Users, label: "Mutlu Müşteri", value: "5000+" },
    { icon: Globe, label: "Hizmet Verilen İl", value: "81" },
    { icon: Factory, label: "Depo Alanı", value: "3000m²" },
    { icon: Truck, label: "Teslimat Noktası", value: "500+" },
    { icon: Award, label: "Kalite Sertifikası", value: "10+" }
  ];

  return (
    <div className="min-h-screen pt-20 pb-16">
      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-primary/5 to-purple-500/5">
        <div className="container mx-auto text-center">
          <Badge variant="outline" className="mb-6">Hakkımızda</Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            20 Yıldır Güvenilir Ortağınız
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            2003 yılından bu yana gıda sektöründe faaliyet gösteren HORECA, 
            kaliteli ürünler ve güvenilir hizmet anlayışıyla HORECA sektörünün 
            vazgeçilmez tedarikçisi konumundadır.
          </p>
          <Button size="lg">
            İletişime Geçin
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
        </div>
      </section>

      {/* Stats */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Icon className="w-6 h-6 text-primary" />
                  </div>
                  <div className="text-2xl font-bold mb-1">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Story */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Hikayemiz
              </h2>
              <p className="text-lg text-muted-foreground mb-6">
                HORECA Gıda Toptancısı, 2003 yılında küçük bir aile işletmesi olarak kuruldu. 
                Kuruluşumuzdan bu yana, kaliteli gıda ürünlerini uygun fiyatlarla müşterilerimize 
                ulaştırma misyonuyla hareket ediyoruz.
              </p>
              <p className="text-lg text-muted-foreground mb-6">
                Başlangıçta sadece yerel pazara hizmet veren şirketimiz, zamanla büyüyerek 
                Türkiye'nin 81 iline ve Kıbrıs'a hizmet veren güçlü bir tedarikçi haline geldi. 
                Bugün 3.000 m² kapalı alan ve 2.500 m² açık alana sahip modern tesisimizde, 
                5.000'den fazla ürün çeşidi ile hizmet veriyoruz.
              </p>
              <Button variant="outline">
                Daha Fazla Bilgi
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop"
                alt="HORECA Tesisleri"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Tarihçemiz
            </h2>
            <p className="text-xl text-muted-foreground">
              20 yıllık yolculuğumuzda önemli kilometre taşları
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-border"></div>
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}`}>
                  <div className={`flex-1 ${index % 2 === 0 ? 'md:text-right md:pr-8' : 'md:text-left md:pl-8'}`}>
                    <Card className="max-w-md mx-auto md:mx-0">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline">{milestone.year}</Badge>
                        </div>
                        <CardTitle>{milestone.title}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription>{milestone.description}</CardDescription>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="w-4 h-4 bg-primary rounded-full border-4 border-background z-10 flex-shrink-0 mx-4"></div>
                  <div className="flex-1"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Değerlerimiz
            </h2>
            <p className="text-xl text-muted-foreground">
              Çalışma prensiplerimizi oluşturan temel değerler
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-8 h-8 text-primary" />
                    </div>
                    <CardTitle>{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{value.description}</CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ekibimiz
            </h2>
            <p className="text-xl text-muted-foreground">
              Deneyimli ve uzman kadromuzla hizmetinizdeyiz
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="w-24 h-24 mx-auto mb-4 overflow-hidden rounded-full">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardTitle className="text-lg">{member.name}</CardTitle>
                  <CardDescription className="text-primary font-medium">
                    {member.position}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{member.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-primary text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Bizimle Çalışmaya Başlayın
          </h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            20 yıllık deneyimimiz ve güvenilir hizmet anlayışımızla 
            işletmenizin ihtiyaçlarını karşılamaya hazırız.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary">
              İletişime Geçin
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
              Katalog İndirin
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
