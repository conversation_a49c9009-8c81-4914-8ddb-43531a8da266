<div class="product-parameters">
  {%- unless product.has_only_default_variant -%}
    {%- if block.settings.picker_type == 'button' -%}
      <variant-radios
        id="variant-radios-{{ section.id }}"
        class="no-js-hidden"
        data-section="{{ section.id }}"
        data-url="{{ product.url }}"
        {% if update_url == false %}
          data-update-url="false"
        {% endif %}
        {{ block.shopify_attributes }}
      >
        {%- for option in product.options_with_values -%}

          {%- liquid
            assign color_trigger = settings.trigger_swatch | handle | strip
            assign name_option = option.name | handle | strip
            assign is_color_option = false

            if settings.type_color_swatch == 'custom' and color_trigger == name_option
              assign is_color_option = true
            endif

            if settings.type_color_swatch == 'standart'
              for value in option.values
                if value.swatch.color != blank
                  assign is_color_option = true
                endif
              endfor
            endif
          -%}

          <fieldset class="product-form__controls" id="SingleOptionSelector-{{ forloop.index0 }}">
            <legend class="product-form__group-name">
              <span>{{ option.name }}:</span>
              {%- if is_color_option and block.settings.enable_color_name -%}
                <span class="product-form__group-name-color" data-color-name-id="ColorName-{{ section.id }}">
                  {{- option.selected_value -}} 
                </span>
              {%- endif -%}
            </legend>

            <div class="product-form__controls-group">

              {%- if option.name == 'Locker Banks' -%}
                <div style="display:flex;" class="locker-banks-options flex  gap-2">
                  {%- for value in product.options_by_name['Locker Banks'].values -%}
                    {%- assign image_key = value | handleize -%}
                   
                    {%- assign swatch_image = shop.metaobjects.shopify--color-pattern[image_key] -%}
                     
                    <label class="locker-banks-swatch cursor-pointer product-form__group-name  rounded p-1 hover:border-black">
                      <input
                        type="radio"
                        name="options[Locker Banks]"
                        value="{{ value }}"
                        class="hidden"
                        form="{{ product_form_id }}"
                        {% if product.selected_or_first_available_variant.options contains value %}
                          checked
                        {% endif %}
                      >

                      <div class="swatch-image w-16 h-16">
                        {%- if swatch_image != blank -%}
                           
                             <img src="{{ swatch_image.image | img_url: '40x40' }}" alt="{{ value }}" class="object-cover w-full h-full rounded">
                           
                          
                        {%- else -%}
                          <span class="text-xs block text-center mt-5">{{ value }}</span>
                        {%- endif -%}
                      </div>
                    </label>
                  {%- endfor -%}
                </div>

              {%- else -%}

                {% render 'product-variant-options',
                  product: product,
                  option: option,
                  block: block,
                  show_color_swatch: is_color_option
                %}

              {%- endif -%}

            </div>
          </fieldset>

        {%- endfor -%}

        <script type="application/json">
          {{ product.variants | json }}
        </script>

        <script>
          document.addEventListener('DOMContentLoaded', function() {
            var variantsDataElement = document.getElementById('ProductVariants-{{ section.id }}');
            if (variantsDataElement) {
              var variants = JSON.parse(variantsDataElement.textContent);
              console.log('Tüm Varyantlar:', variants);
              variants.forEach(function(variant, index) {
                console.log(`Varyant ${index + 1}:`);
                console.log('ID:', variant.id);
                console.log('Başlık:', variant.title);
                console.log('Fiyat:', variant.price);
                console.log('Option1:', variant.option1);
                console.log('Option2:', variant.option2);
                console.log('Option3:', variant.option3);
                console.log('-----------------------------');
              });
            }
          });
        </script>

      </variant-radios>

    {% else %}
      <variant-selects
        id="variant-selects-{{ section.id }}"
        class="no-js-hidden"
        data-section="{{ section.id }}"
        data-url="{{ product.url }}"
        {% if update_url == false %}
          data-update-url="false"
        {% endif %}
        {{ block.shopify_attributes }}
      >
        {%- for option in product.options_with_values -%}
          <fieldset class="product-form__controls">
            <label for="Option-{{ section.id }}-{{ forloop.index0 }}" class="product-form__group-name">
              {{ option.name }}:
            </label>
            <div class="product-form__controls-group">
              <div class="select">
                <select
                  name="options[{{ option.name | escape }}]"
                  id="Option-{{ section.id }}-{{ forloop.index0 }}"
                  class="select__select"
                  form="{{ product_form_id }}"
                >
                  {% render 'product-variant-options',
                    product: product,
                    option: option,
                    block: block,
                    show_color_swatch: false
                  %}
                </select>
                {% render 'icon-caret' %}
              </div>
            </div>  
          </fieldset>
        {%- endfor -%}
        <script type="application/json">
          {{ product.variants | json }}
        </script>
      </variant-selects>
    {% endif %}
  {% endunless %}

  <noscript class="product-form__noscript-wrapper-{{ section.id }}">
    <fieldset class="product-form__controls {% if product.has_only_default_variant %} hidden{% endif %}">
      <label for="Variants-{{ section.id }}" class="product-form__group-name">{{ 'products.product.product_variants' | t }}:</label>
      <div class="product-form__controls-group">
        <div class="select">
          <select
            name="id"
            id="Variants-{{ section.id }}"
            class="select__select"
            form="{{ product_form_id }}"
          >
            {%- for variant in product.variants -%}
              <option
                {% if variant == product.selected_or_first_available_variant %}
                  selected="selected"
                {% endif %}
                {% if variant.available == false %}
                  disabled
                {% endif %}
                value="{{ variant.id }}"
              >
                {%- liquid
                  echo variant.title
                  echo variant.price | money | strip_html | prepend: ' - '
                  if variant.available == false
                    echo 'products.product.sold_out' | t | prepend: ' - '
                  endif
                  if variant.quantity_rule.increment > 1
                    echo 'products.product.quantity.multiples_of' | t: quantity: variant.quantity_rule.increment | prepend: ' - '
                  endif
                  if variant.quantity_rule.min > 1
                    echo 'products.product.quantity.minimum_of' | t: quantity: variant.quantity_rule.min | prepend: ' - '
                  endif
                  if variant.quantity_rule.max != null
                    echo 'products.product.quantity.maximum_of' | t: quantity: variant.quantity_rule.max | prepend: ' - '
                  endif
                  assign cart_quantity = cart | item_count_for_variant: variant.id
                  if cart_quantity > 0
                    echo 'products.product.quantity.in_cart_html' | t: quantity: cart_quantity | prepend: ' - '
                  endif
                -%}
              </option>
            {%- endfor -%}
          </select>
          {% render 'icon-caret' %}
        </div>
      </div>
    </fieldset>
  </noscript>
</div>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const swatchInputs = document.querySelectorAll(".locker-banks-swatch input[type='radio']");

    swatchInputs.forEach((input) => {
      input.addEventListener("change", function () {
        // Tüm label'ların border'ını sıfırla
        document.querySelectorAll(".locker-banks-swatch").forEach(label => {
          label.style.border = "2px solid transparent";
        });

        // Seçilen input'un parent label'ına mavi border ekle
        if (this.checked) {
          const label = this.closest(".locker-banks-swatch");
          label.style.border = "2px solid #02437D"; // mavi (Tailwind blue-600)
        }
      });

      // Sayfa yüklenince checked olan input varsa ona border ver
      if (input.checked) {
        const label = input.closest(".locker-banks-swatch");
        label.style.border = "2px solid #02437D";
      }
    });
  });
</script>
