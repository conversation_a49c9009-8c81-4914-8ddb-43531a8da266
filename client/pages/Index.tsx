import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  Truck, 
  Clock, 
  Shield, 
  Star, 
  Package,
  ArrowRight,
  Check,
  Users,
  MapPin,
  Award,
  Factory,
  ChevronLeft,
  ChevronRight,
  Phone,
  Mail
} from 'lucide-react';

export default function Index() {
  const [currentSlide, setCurrentSlide] = useState(0);

  const sliderImages = [
    {
      url: "/1.1.png",
      title: "Kaliteli Gıda Ürünleri",
      description: "2003'ten beri güvenilir tedarikçiniz"
    },
    {
      url: "/2.jpg",
      title: "3.000 m² Kapalı Depo",
      description: "Modern depolama ve lojistik hizmetleri"
    },
    {
      url: "/3.1.png",
      title: "HORECA Çözümleri",
      description: "Otel, restoran ve cafe işletmelerine özel"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderImages.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % sliderImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + sliderImages.length) % sliderImages.length);
  };

  const productCategories = [
    {
      title: "Süt Ürünleri",
      image: "https://images.unsplash.com/photo-1550583724-b2692b85b150?w=300&h=200&fit=crop",
      description: "Peynir, yoğurt, süt ve tüm süt ürünleri"
    },
    {
      title: "Et ve Şarküteri", 
      image: "https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=300&h=200&fit=crop",
      description: "Taze et, donuk et ve şarküteri ürünleri"
    },
    {
      title: "Meyve & Sebze",
      image: "https://images.unsplash.com/photo-1506976785307-8732e854ad03?w=300&h=200&fit=crop", 
      description: "Taze meyve ve sebze çeşitleri"
    },
    {
      title: "Deniz Ürünleri",
      image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=300&h=200&fit=crop",
      description: "Taze ve donuk balık çeşitleri"
    },
    {
      title: "Soslar & Baharatlar",
      image: "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300&h=200&fit=crop",
      description: "Çeşitli sos ve baharat ürünleri"
    },
    {
      title: "Kuru Gıdalar",
      image: "https://images.unsplash.com/photo-1586201375761-83865001e31c?w=300&h=200&fit=crop",
      description: "Makarna, pirinç, bakliyat ve kuru gıdalar"
    }
  ];

  const features = [
    {
      icon: Factory,
      title: "20+ Yıl Deneyim",
      description: "2003'ten beri gıda sektöründe güvenilir hizmet"
    },
    {
      icon: MapPin,
      title: "81 İl Dağıtım",
      description: "Türkiye geneli ve Kıbrıs'a teslimat"
    },
    {
      icon: Package,
      title: "3.000 m² Depo",
      description: "Modern depolama ve soğuk hava sistemi"
    },
    {
      icon: Truck,
      title: "Hızlı Lojistik",
      description: "Aynı gün ve ertesi gün teslimat"
    },
    {
      icon: Shield,
      title: "Kalite Garantisi",
      description: "ISO ve HACCP sertifikalı ürünler"
    },
    {
      icon: Award,
      title: "Güvenilir Tedarikçi",
      description: "Binlerce mutlu müşteri referansı"
    }
  ];

  const stats = [
    { label: "İl", value: "81", icon: MapPin },
    { label: "Yıllık Deneyim", value: "20+", icon: Award },
    { label: "Depo Alanı", value: "3.000m²", icon: Factory },
    { label: "Ürün Çeşidi", value: "5.000+", icon: Package }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Slider Section */}
      <section className="relative h-[500px] md:h-[600px] overflow-hidden mt-16">
        <div className="relative h-full">
          {sliderImages.map((slide, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-1000 ${
                index === currentSlide ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <img
                src={slide.url}
                alt={slide.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/50" />
              <div className="absolute inset-0 flex items-center">
                <div className="container mx-auto px-4">
                  <div className="max-w-2xl text-white">
                    <h1 className="text-4xl md:text-6xl font-bold mb-4">{slide.title}</h1>
                    <p className="text-xl md:text-2xl mb-6">{slide.description}</p>
                    <Button size="lg" className="bg-primary hover:bg-primary/90 text-lg px-8">
                      Keşfedin
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {/* Navigation buttons */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-colors z-10"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-colors z-10"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
          
          {/* Dots indicator */}
          <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex space-x-3">
            {sliderImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-4 h-4 rounded-full transition-colors ${
                  index === currentSlide ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Company Stats */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            {stats.map((stat) => {
              const Icon = stat.icon;
              return (
                <div key={stat.label} className="flex flex-col items-center">
                  <Icon className="w-8 h-8 mb-3" />
                  <div className="text-3xl md:text-4xl font-bold mb-1">{stat.value}</div>
                  <div className="text-sm opacity-90">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge variant="outline" className="mb-4">Hakkımızda</Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Türkiye'nin Güvenilir Gıda Toptancısı
              </h2>
              <p className="text-lg text-muted-foreground mb-6">
                2003 yılından bu yana gıda sektöründe faaliyet gösteren şirketimiz, 
                3.000 m² kapalı alan ve 2.500 m² açık alana sahip modern tesisimizde, 
                Türkiye'nin 81 ili ve Kıbrıs'a hizmet vermektedir.
              </p>
              <div className="space-y-3 mb-8">
                <div className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-primary" />
                  <span>20+ yıl sektör deneyimi</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-primary" />
                  <span>Modern depolama ve soğuk hava sistemi</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-primary" />
                  <span>ISO ve HACCP kalite sertifikaları</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-primary" />
                  <span>Güvenilir tedarik zinciri</span>
                </div>
              </div>
              <Button size="lg">
                Daha Fazla Bilgi
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop"
                alt="Gıda Deposu"
                className="rounded-lg shadow-lg w-full"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-6 rounded-lg shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">5000+</div>
                  <div className="text-sm text-muted-foreground">Ürün Çeşidi</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Product Categories */}
      <section id="products" className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">Ürünlerimiz</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Geniş Ürün Yelpazesi
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Süt ürünlerinden deniz ürünlerine, meyve sebzeden soslar ve baharatlara 
              kadar geniş ürün gamımızla hizmetinizdeyiz.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {productCategories.map((category, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
                <div className="relative overflow-hidden">
                  <img
                    src={category.image}
                    alt={category.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
                </div>
                <CardHeader>
                  <CardTitle className="text-xl">{category.title}</CardTitle>
                  <CardDescription>{category.description}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button size="lg" variant="outline">
              Tüm Ürünleri Görüntüle
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">Neden Biz?</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Sektördeki Tecrübemiz ve Avantajlarımız
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => {
              const Icon = feature.icon;
              return (
                <div key={feature.title} className="text-center">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                  <p className="text-muted-foreground">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-primary text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            İşletmeniz İçin En Uygun Çözümü Birlikte Bulalım
          </h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Uzman ekibimiz sizin için en uygun ürün ve hizmet paketini hazırlamak için bekliyor. 
            Hemen iletişime geçin ve özel fiyat teklifi alın.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="text-lg px-8">
              <Phone className="mr-2 w-5 h-5" />
              Hemen Arayın
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 border-white text-black hover:bg-white hover:text-primary">
              <Mail className="mr-2 w-5 h-5" />
              E-posta Gönder
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
