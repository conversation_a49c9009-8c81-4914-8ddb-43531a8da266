import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Truck, 
  Clock, 
  Shield, 
  Package,
  Thermometer,
  MapPin,
  Phone,
  Calculator,
  Users,
  Target,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

export default function Services() {
  const mainServices = [
    {
      icon: Truck,
      title: "Hızlı Teslimat",
      description: "Aynı gün ve ertesi gün teslimat seçenekleri",
      features: [
        "Aynı gün teslimat (şehir içi)",
        "<PERSON><PERSON><PERSON> gün teslimat (şehir dışı)",
        "Programlı teslimat",
        "Acil teslimat hizmeti"
      ],
      image: "https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=400&h=300&fit=crop"
    },
    {
      icon: Thermometer,
      title: "<PERSON><PERSON><PERSON> Zincir",
      description: "Ür<PERSON><PERSON><PERSON>inizi en taze haliyle teslim ediyoruz",
      features: [
        "Soğutmalı araç filosu",
        "Sıcaklık takip sistemi",
        "Donuk ürün taşımacılığı",
        "Kalite garantili taşıma"
      ],
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop"
    },
    {
      icon: Package,
      title: "Depolama Hizmetleri",
      description: "Modern depolama imkanları ile güvenli saklama",
      features: [
        "3.000 m² kapalı depo",
        "Soğuk hava depoları",
        "Kuru gıda depoları",
        "Güvenlik kamera sistemleri"
      ],
      image: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=400&h=300&fit=crop"
    },
    {
      icon: Calculator,
      title: "Fiyat Danışmanlığı",
      description: "En uygun fiyat tekliflerini hazırlıyoruz",
      features: [
        "Toplu alım indirimleri",
        "Özel fiyat teklifleri",
        "Kampanya ve promosyonlar",
        "Ödeme kolaylıkları"
      ],
      image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=300&fit=crop"
    }
  ];

  const additionalServices = [
    {
      icon: Users,
      title: "Müşteri Danışmanlığı",
      description: "Uzman ekibimizle 7/24 destek hizmeti"
    },
    {
      icon: Target,
      title: "Özel Tedarik",
      description: "İhtiyacınıza özel ürün tedarik hizmeti"
    },
    {
      icon: Shield,
      title: "Kalite Kontrolü",
      description: "Tüm ürünlerde kalite kontrol ve sertifikasyon"
    },
    {
      icon: Phone,
      title: "Sipariş Takibi",
      description: "Online sipariş takip ve bilgilendirme sistemi"
    },
    {
      icon: MapPin,
      title: "Bölgesel Hizmet",
      description: "81 il ve Kıbrıs'a yaygın hizmet ağı"
    },
    {
      icon: Clock,
      title: "Esnek Saatler",
      description: "Esnek teslimat saatleri ve acil sipariş desteği"
    }
  ];

  const deliveryAreas = [
    "İstanbul", "Ankara", "İzmir", "Bursa", "Antalya", "Adana", "Konya", "Gaziantep", 
    "Kayseri", "Mersin", "Diyarbakır", "Eskişehir", "Samsun", "Denizli", "Malatya",
    "Kahramanmaraş", "Erzurum", "Van", "Batman", "Elazığ", "Iğdır", "Kıbrıs"
  ];

  const processSteps = [
    {
      step: "1",
      title: "Sipariş",
      description: "Online platformumuz veya telefon ile sipariş verin"
    },
    {
      step: "2",
      title: "Onay",
      description: "Siparişiniz onaylanır ve ödeme seçenekleri sunulur"
    },
    {
      step: "3",
      title: "Hazırlık",
      description: "Ürünleriniz kalite kontrolünden geçirilerek hazırlanır"
    },
    {
      step: "4",
      title: "Teslimat",
      description: "Belirtilen adrese ve zamanda güvenle teslim edilir"
    }
  ];

  return (
    <div className="min-h-screen pt-20 pb-16">
      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-primary/5 to-purple-500/5">
        <div className="container mx-auto text-center">
          <Badge variant="outline" className="mb-6">Hizmetlerimiz</Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Kapsamlı Hizmet Çözümleri
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Gıda tedarik zincirinde ihtiyaç duyduğunuz tüm hizmetleri 
            tek noktadan profesyonel ekibimizle sunuyoruz.
          </p>
          <Button size="lg">
            Hizmet Talebi
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
        </div>
      </section>

      {/* Main Services */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ana Hizmetlerimiz
            </h2>
            <p className="text-xl text-muted-foreground">
              İşletmenizin tüm ihtiyaçlarını karşılayan hizmet yelpazesi
            </p>
          </div>

          <div className="space-y-16">
            {mainServices.map((service, index) => {
              const Icon = service.icon;
              return (
                <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                  <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                    <div className="flex items-center mb-6">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
                        <Icon className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold">{service.title}</h3>
                        <p className="text-muted-foreground">{service.description}</p>
                      </div>
                    </div>
                    
                    <div className="space-y-3 mb-6">
                      {service.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-primary" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <Button variant="outline">
                      Detaylı Bilgi
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </div>
                  
                  <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                    <img
                      src={service.image}
                      alt={service.title}
                      className="rounded-lg shadow-lg w-full"
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Additional Services */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ek Hizmetlerimiz
            </h2>
            <p className="text-xl text-muted-foreground">
              İhtiyaçlarınızı destekleyen ek hizmet seçenekleri
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalServices.map((service, index) => {
              const Icon = service.icon;
              return (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-8 h-8 text-primary" />
                    </div>
                    <CardTitle>{service.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {service.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Hizmet Sürecimiz
            </h2>
            <p className="text-xl text-muted-foreground">
              4 adımda hızlı ve güvenilir hizmet deneyimi
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <Card key={index} className="relative text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4 text-white text-xl font-bold">
                    {step.step}
                  </div>
                  <CardTitle>{step.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {step.description}
                  </CardDescription>
                </CardContent>
                {index < processSteps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                    <ArrowRight className="w-6 h-6 text-primary" />
                  </div>
                )}
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Delivery Areas */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Hizmet Bölgelerimiz
            </h2>
            <p className="text-xl text-muted-foreground">
              Türkiye'nin 81 ili ve Kıbrıs'a güvenilir teslimat
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            {deliveryAreas.map((area, index) => (
              <Badge key={index} variant="outline" className="justify-center py-2">
                {area}
              </Badge>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-4">
              Listelenen bölgelerin dışında hizmet almak için
            </p>
            <Button variant="outline">
              <Phone className="w-4 h-4 mr-2" />
              Bizi Arayın
            </Button>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-primary text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Hizmetlerimizden Yararlanın
          </h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Profesyonel hizmet anlayışımızla işletmenizin tüm gıda tedarik 
            ihtiyaçlarını karşılamaya hazırız.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary">
              <Phone className="mr-2 w-5 h-5" />
              Hemen Arayın
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
              Online Sipariş
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
