import { useState, useEffect } from 'react';
import { pageService } from '@/lib/firebaseService';
import DynamicPage from '@/components/DynamicPage';
import Index from './Index'; // Fallback to original homepage

export default function DynamicHome() {
  const [hasCustomHome, setHasCustomHome] = useState<boolean>(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    checkCustomHomePage();
  }, []);

  const checkCustomHomePage = async () => {
    try {
      // Set a 2 second timeout for Firebase check
      const timeoutPromise = new Promise<null>((_, reject) => 
        setTimeout(() => reject(new Error('Firebase timeout')), 2000)
      );
      
      const homePagePromise = pageService.getPageBySlug('home');
      
      const homePage = await Promise.race([homePagePromise, timeoutPromise]);
      
      if (homePage && homePage.isPublished) {
        setHasCustomHome(true);
      }
    } catch (error) {
      console.warn('Firebase not available, using static content:', error.message);
      // Keep hasCustomHome as false to show static content
    } finally {
      setIsChecking(false);
    }
  };

  // Always show static content while checking or if no custom home
  if (isChecking || !hasCustomHome) {
    return <Index />;
  }

  // If there's a custom home page and Firebase is working, use DynamicPage
  return <DynamicPage slug="home" />;
}
