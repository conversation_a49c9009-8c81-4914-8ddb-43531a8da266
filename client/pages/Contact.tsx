import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock,
  Building,
  Fax,
  Globe,
  MessageSquare,
  Send,
  Navigation
} from 'lucide-react';

export default function Contact() {
  const contactInfo = [
    {
      icon: Phone,
      title: "Telefon",
      details: [
        "+90 (*************",
        "+90 (*************"
      ]
    },
    {
      icon: Mail,
      title: "E-posta",
      details: [
        "<EMAIL>",
        "<EMAIL>"
      ]
    },
    {
      icon: MapPin,
      title: "Adres",
      details: [
        "HORECA Gıda Toptancısı",
        "Atatürk Mahallesi, Gıda Caddesi No:123",
        "34000 Beyoğlu/İstanbul"
      ]
    },
    {
      icon: Clock,
      title: "Çalışma Saatleri",
      details: [
        "Pazartesi - Cuma: 08:00 - 18:00",
        "Cumartesi: 08:00 - 16:00",
        "Pazar: Kapalı"
      ]
    }
  ];

  const departments = [
    {
      name: "Satış Departmanı",
      phone: "+90 (*************",
      email: "<EMAIL>",
      description: "Ürün bilgileri ve sipariş desteği"
    },
    {
      name: "Müşteri Hizmetleri",
      phone: "+90 (*************",
      email: "<EMAIL>",
      description: "Genel sorular ve destek"
    },
    {
      name: "Muhasebe",
      phone: "+90 (*************",
      email: "<EMAIL>",
      description: "Faturalandırma ve ödeme"
    },
    {
      name: "Lojistik",
      phone: "+90 (*************",
      email: "<EMAIL>",
      description: "Teslimat ve kargo takibi"
    }
  ];

  const branches = [
    {
      city: "İstanbul",
      address: "Atatürk Mahallesi, Gıda Caddesi No:123, 34000 Beyoğlu",
      phone: "+90 (*************",
      manager: "Ahmet Yılmaz"
    },
    {
      city: "Ankara",
      address: "Çankaya Mahallesi, Ticaret Sokak No:45, 06000 Çankaya",
      phone: "+90 (*************",
      manager: "Fatma Demir"
    },
    {
      city: "İzmir",
      address: "Alsancak Mahallesi, Liman Caddesi No:67, 35000 Konak",
      phone: "+90 (*************",
      manager: "Mehmet Kaya"
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Form gönderme işlemi
    alert('Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.');
  };

  return (
    <div className="min-h-screen pt-20 pb-16">
      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-primary/5 to-purple-500/5">
        <div className="container mx-auto text-center">
          <Badge variant="outline" className="mb-6">İletişim</Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Bizimle İletişime Geçin
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Sorularınız, önerileriniz veya sipariş talepleriniz için 
            7/24 hizmetinizdeyiz. Hemen iletişime geçin!
          </p>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="lg:order-1">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="w-5 h-5 mr-2" />
                  Mesaj Gönder
                </CardTitle>
                <CardDescription>
                  Formu doldurun, size en kısa sürede dönüş yapalım
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">Ad *</Label>
                      <Input id="firstName" placeholder="Adınız" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Soyad *</Label>
                      <Input id="lastName" placeholder="Soyadınız" required />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email">E-posta *</Label>
                    <Input id="email" type="email" placeholder="<EMAIL>" required />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone">Telefon</Label>
                    <Input id="phone" placeholder="+90 (555) 123 45 67" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="company">Şirket/İşletme</Label>
                    <Input id="company" placeholder="Şirket adınız" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="subject">Konu *</Label>
                    <Input id="subject" placeholder="Mesaj konusu" required />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="message">Mesaj *</Label>
                    <Textarea 
                      id="message" 
                      placeholder="Mesajınızı buraya yazın..." 
                      rows={5}
                      required 
                    />
                  </div>
                  
                  <Button type="submit" className="w-full">
                    <Send className="w-4 h-4 mr-2" />
                    Mesaj Gönder
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <div className="space-y-6 lg:order-2">
              <div className="space-y-6">
                {contactInfo.map((info, index) => {
                  const Icon = info.icon;
                  return (
                    <Card key={index}>
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center text-lg">
                          <Icon className="w-5 h-5 mr-2 text-primary" />
                          {info.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        {info.details.map((detail, idx) => (
                          <p key={idx} className="text-muted-foreground">
                            {detail}
                          </p>
                        ))}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Hızlı İletişim</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Phone className="w-4 h-4 mr-2" />
                    Bizi Arayın
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Mail className="w-4 h-4 mr-2" />
                    E-posta Gönder
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Navigation className="w-4 h-4 mr-2" />
                    Yol Tarifi Al
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Departments */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Departmanlarımız
            </h2>
            <p className="text-xl text-muted-foreground">
              İhtiyacınıza göre doğru departmanla iletişime geçin
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {departments.map((dept, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building className="w-5 h-5 mr-2 text-primary" />
                    {dept.name}
                  </CardTitle>
                  <CardDescription>{dept.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{dept.phone}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{dept.email}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Branches */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Şubelerimiz
            </h2>
            <p className="text-xl text-muted-foreground">
              Türkiye'nin önemli şehirlerinde hizmet noktalarımız
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {branches.map((branch, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <CardTitle className="text-2xl text-primary">{branch.city}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <MapPin className="w-4 h-4 text-muted-foreground mt-1 flex-shrink-0" />
                    <span className="text-sm text-left">{branch.address}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{branch.phone}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Building className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">Şube Müdürü: {branch.manager}</span>
                  </div>
                  <Button variant="outline" size="sm" className="w-full mt-4">
                    <Navigation className="w-4 h-4 mr-2" />
                    Yol Tarifi
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Konumumuz
            </h2>
            <p className="text-xl text-muted-foreground">
              Ana merkez ofisimizi haritada görüntüleyin
            </p>
          </div>

          <div className="bg-gray-200 h-96 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-16 h-16 text-primary mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Harita Yükleniyor</h3>
              <p className="text-muted-foreground">
                Google Maps entegrasyonu burada yer alacak
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-primary text-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Hemen İletişime Geçin
          </h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Uzman ekibimiz sizin için en uygun çözümleri sunmaya hazır. 
            Arayın, yazın veya ziyaret edin!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary">
              <Phone className="mr-2 w-5 h-5" />
              +90 (*************
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
              <Mail className="mr-2 w-5 h-5" />
              <EMAIL>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
